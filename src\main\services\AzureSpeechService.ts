import * as sdk from 'microsoft-cognitiveservices-speech-sdk';
import { EventEmitter } from 'events';
import { SpeechToTextResult, AzureConfig } from '../../shared/types';
import { ERROR_CODES, API_CONFIG } from '../../shared/constants';
import { createError, retry, blobToArrayBuffer } from '../../shared/utils';

export class AzureSpeechService extends EventEmitter {
  private speechConfig: sdk.SpeechConfig | null = null;
  private recognizer: sdk.SpeechRecognizer | null = null;

  constructor() {
    super();
  }

  /**
   * 初始化 Azure Speech Service
   */
  initialize(config: AzureConfig['speech']): void {
    try {
      this.speechConfig = sdk.SpeechConfig.fromSubscription(
        config.apiKey,
        config.region
      );
      
      // 設置語言
      this.speechConfig.speechRecognitionLanguage = config.language;
      
      // 設置輸出格式
      this.speechConfig.outputFormat = sdk.OutputFormat.Detailed;
      
      // 設置其他配置
      this.speechConfig.setProperty(
        sdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs,
        '5000'
      );
      
      this.speechConfig.setProperty(
        sdk.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs,
        '3000'
      );

      this.emit('initialized');
    } catch (error) {
      throw createError(
        `Azure Speech Service 初始化失敗: ${(error as Error).message}`,
        ERROR_CODES.SPEECH_SERVICE_ERROR,
        error
      );
    }
  }

  /**
   * 語音轉文字 - 從音頻 Blob
   */
  async transcribe(
    audioBlob: Blob,
    config: AzureConfig['speech']
  ): Promise<SpeechToTextResult> {
    if (!this.speechConfig) {
      this.initialize(config);
    }

    try {
      return await retry(async () => {
        return await this.performTranscription(audioBlob);
      }, API_CONFIG.retryAttempts, API_CONFIG.retryDelay);
    } catch (error) {
      throw createError(
        `語音轉文字失敗: ${(error as Error).message}`,
        ERROR_CODES.SPEECH_SERVICE_ERROR,
        error
      );
    }
  }

  /**
   * 即時語音識別 - 從音頻流
   */
  async startContinuousRecognition(
    audioStream: MediaStream,
    config: AzureConfig['speech'],
    onResult: (result: SpeechToTextResult) => void,
    onError: (error: Error) => void
  ): Promise<void> {
    if (!this.speechConfig) {
      this.initialize(config);
    }

    try {
      // 創建音頻配置
      const audioConfig = this.createAudioConfigFromStream(audioStream);
      
      // 創建語音識別器
      this.recognizer = new sdk.SpeechRecognizer(this.speechConfig!, audioConfig);

      // 設置事件監聽器
      this.setupRecognizerEvents(onResult, onError);

      // 開始連續識別
      this.recognizer.startContinuousRecognitionAsync(
        () => {
          this.emit('recognition-started');
        },
        (error) => {
          onError(createError(
            `連續語音識別啟動失敗: ${error}`,
            ERROR_CODES.SPEECH_SERVICE_ERROR
          ));
        }
      );
    } catch (error) {
      throw createError(
        `連續語音識別失敗: ${(error as Error).message}`,
        ERROR_CODES.SPEECH_SERVICE_ERROR,
        error
      );
    }
  }

  /**
   * 停止連續語音識別
   */
  async stopContinuousRecognition(): Promise<void> {
    if (!this.recognizer) return;

    return new Promise((resolve, reject) => {
      this.recognizer!.stopContinuousRecognitionAsync(
        () => {
          this.cleanup();
          this.emit('recognition-stopped');
          resolve();
        },
        (error) => {
          this.cleanup();
          reject(createError(
            `停止連續語音識別失敗: ${error}`,
            ERROR_CODES.SPEECH_SERVICE_ERROR
          ));
        }
      );
    });
  }

  /**
   * 檢查服務是否可用
   */
  async testConnection(config: AzureConfig['speech']): Promise<boolean> {
    try {
      // 創建測試用的語音配置
      const testConfig = sdk.SpeechConfig.fromSubscription(
        config.apiKey,
        config.region
      );
      
      // 創建空的音頻配置進行測試
      const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput();
      const recognizer = new sdk.SpeechRecognizer(testConfig, audioConfig);

      return new Promise((resolve) => {
        recognizer.recognizeOnceAsync(
          () => {
            recognizer.close();
            resolve(true);
          },
          () => {
            recognizer.close();
            resolve(false);
          }
        );
        
        // 設置超時
        setTimeout(() => {
          recognizer.close();
          resolve(false);
        }, 5000);
      });
    } catch {
      return false;
    }
  }

  /**
   * 執行語音轉文字
   */
  private async performTranscription(audioBlob: Blob): Promise<SpeechToTextResult> {
    if (!this.speechConfig) {
      throw createError('Speech Service 未初始化', ERROR_CODES.SPEECH_SERVICE_ERROR);
    }

    return new Promise(async (resolve, reject) => {
      try {
        // 將 Blob 轉換為 ArrayBuffer
        const arrayBuffer = await blobToArrayBuffer(audioBlob);
        
        // 創建音頻配置
        const audioConfig = sdk.AudioConfig.fromWavFileInput(
          new Uint8Array(arrayBuffer)
        );
        
        // 創建語音識別器
        const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig);

        // 執行一次性識別
        recognizer.recognizeOnceAsync(
          (result) => {
            recognizer.close();
            
            if (result.reason === sdk.ResultReason.RecognizedSpeech) {
              resolve({
                text: result.text,
                confidence: this.extractConfidence(result)
              });
            } else if (result.reason === sdk.ResultReason.NoMatch) {
              reject(createError(
                '無法識別語音內容',
                ERROR_CODES.SPEECH_SERVICE_ERROR
              ));
            } else {
              reject(createError(
                `語音識別失敗: ${result.errorDetails}`,
                ERROR_CODES.SPEECH_SERVICE_ERROR
              ));
            }
          },
          (error) => {
            recognizer.close();
            reject(createError(
              `語音識別錯誤: ${error}`,
              ERROR_CODES.SPEECH_SERVICE_ERROR
            ));
          }
        );

        // 設置超時
        setTimeout(() => {
          recognizer.close();
          reject(createError(
            '語音識別超時',
            ERROR_CODES.SPEECH_SERVICE_ERROR
          ));
        }, API_CONFIG.timeout);

      } catch (error) {
        reject(createError(
          `語音轉文字處理失敗: ${(error as Error).message}`,
          ERROR_CODES.SPEECH_SERVICE_ERROR,
          error
        ));
      }
    });
  }

  /**
   * 從音頻流創建音頻配置
   */
  private createAudioConfigFromStream(audioStream: MediaStream): sdk.AudioConfig {
    // 注意：這裡需要將 MediaStream 轉換為 Azure SDK 可接受的格式
    // 實際實現可能需要使用 Web Audio API 進行處理
    return sdk.AudioConfig.fromDefaultMicrophoneInput();
  }

  /**
   * 設置識別器事件監聽器
   */
  private setupRecognizerEvents(
    onResult: (result: SpeechToTextResult) => void,
    onError: (error: Error) => void
  ): void {
    if (!this.recognizer) return;

    // 識別結果事件
    this.recognizer.recognized = (_, e) => {
      if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
        onResult({
          text: e.result.text,
          confidence: this.extractConfidence(e.result)
        });
      }
    };

    // 正在識別事件
    this.recognizer.recognizing = (_, e) => {
      this.emit('recognizing', e.result.text);
    };

    // 錯誤事件
    this.recognizer.canceled = (_, e) => {
      if (e.reason === sdk.CancellationReason.Error) {
        onError(createError(
          `語音識別被取消: ${e.errorDetails}`,
          ERROR_CODES.SPEECH_SERVICE_ERROR
        ));
      }
    };
  }

  /**
   * 提取信心度分數
   */
  private extractConfidence(result: sdk.SpeechRecognitionResult): number {
    try {
      // 從詳細結果中提取信心度
      const detailedResult = JSON.parse(result.json);
      if (detailedResult.NBest && detailedResult.NBest.length > 0) {
        return detailedResult.NBest[0].Confidence || 0.5;
      }
    } catch {
      // 如果無法解析，返回預設值
    }
    return 0.5;
  }

  /**
   * 清理資源
   */
  private cleanup(): void {
    if (this.recognizer) {
      this.recognizer.close();
      this.recognizer = null;
    }
  }

  /**
   * 銷毀服務
   */
  destroy(): void {
    this.cleanup();
    this.speechConfig = null;
    this.removeAllListeners();
  }
}
