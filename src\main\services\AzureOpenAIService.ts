import axios, { AxiosInstance } from 'axios';
import { EventEmitter } from 'events';
import { AIProcessResult, AzureConfig } from '../../shared/types';
import { ERROR_CODES, API_CONFIG } from '../../shared/constants';
import { createError, retry, blobToArrayBuffer } from '../../shared/utils';

export class AzureOpenAIService extends EventEmitter {
  private httpClient: AxiosInstance | null = null;
  private config: AzureConfig['openai'] | null = null;

  constructor() {
    super();
  }

  /**
   * 初始化 Azure OpenAI Service
   */
  initialize(config: AzureConfig['openai']): void {
    try {
      this.config = config;
      
      // 創建 HTTP 客戶端
      this.httpClient = axios.create({
        baseURL: config.endpoint,
        timeout: API_CONFIG.timeout,
        headers: {
          'api-key': config.apiKey,
          'Content-Type': 'application/json'
        }
      });

      // 設置請求攔截器
      this.httpClient.interceptors.request.use(
        (config) => {
          this.emit('request-start', config);
          return config;
        },
        (error) => {
          this.emit('request-error', error);
          return Promise.reject(error);
        }
      );

      // 設置回應攔截器
      this.httpClient.interceptors.response.use(
        (response) => {
          this.emit('response-success', response);
          return response;
        },
        (error) => {
          this.emit('response-error', error);
          return Promise.reject(error);
        }
      );

      this.emit('initialized');
    } catch (error) {
      throw createError(
        `Azure OpenAI Service 初始化失敗: ${(error as Error).message}`,
        ERROR_CODES.OPENAI_SERVICE_ERROR,
        error
      );
    }
  }

  /**
   * 處理音頻 - 使用 gpt-4o-mini-audio-preview 模型
   */
  async processAudio(
    audioBlob: Blob,
    config: AzureConfig['openai']
  ): Promise<AIProcessResult> {
    if (!this.httpClient) {
      this.initialize(config);
    }

    try {
      return await retry(async () => {
        return await this.performAudioProcessing(audioBlob);
      }, API_CONFIG.retryAttempts, API_CONFIG.retryDelay);
    } catch (error) {
      throw createError(
        `AI 音頻處理失敗: ${(error as Error).message}`,
        ERROR_CODES.OPENAI_SERVICE_ERROR,
        error
      );
    }
  }

  /**
   * 處理文字指令 - 使用文字模型
   */
  async processText(
    text: string,
    config: AzureConfig['openai'],
    context?: string
  ): Promise<AIProcessResult> {
    if (!this.httpClient) {
      this.initialize(config);
    }

    try {
      return await retry(async () => {
        return await this.performTextProcessing(text, context);
      }, API_CONFIG.retryAttempts, API_CONFIG.retryDelay);
    } catch (error) {
      throw createError(
        `AI 文字處理失敗: ${(error as Error).message}`,
        ERROR_CODES.OPENAI_SERVICE_ERROR,
        error
      );
    }
  }

  /**
   * 檢查服務連接
   */
  async testConnection(config: AzureConfig['openai']): Promise<boolean> {
    try {
      if (!this.httpClient) {
        this.initialize(config);
      }

      const response = await this.httpClient!.get('/models');
      return response.status === 200;
    } catch {
      return false;
    }
  }

  /**
   * 執行音頻處理
   */
  private async performAudioProcessing(audioBlob: Blob): Promise<AIProcessResult> {
    if (!this.httpClient || !this.config) {
      throw createError('OpenAI Service 未初始化', ERROR_CODES.OPENAI_SERVICE_ERROR);
    }

    try {
      // 將音頻轉換為 base64
      const arrayBuffer = await blobToArrayBuffer(audioBlob);
      const base64Audio = Buffer.from(arrayBuffer).toString('base64');

      // 構建請求
      const requestData = {
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: '請理解這段語音內容，並根據指示提供適當的回應。如果是寫作任務，請直接提供內容；如果是問題，請提供答案；如果是指令，請執行相應操作。'
              },
              {
                type: 'input_audio',
                input_audio: {
                  data: base64Audio,
                  format: 'webm'
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.7
      };

      const response = await this.httpClient.post(
        `/openai/deployments/${this.config.model}/chat/completions?api-version=2024-10-01-preview`,
        requestData
      );

      return this.parseResponse(response.data);
    } catch (error) {
      throw this.handleAPIError(error);
    }
  }

  /**
   * 執行文字處理
   */
  private async performTextProcessing(
    text: string,
    context?: string
  ): Promise<AIProcessResult> {
    if (!this.httpClient || !this.config) {
      throw createError('OpenAI Service 未初始化', ERROR_CODES.OPENAI_SERVICE_ERROR);
    }

    try {
      const requestData = {
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: context 
              ? `上下文：${context}\n\n用戶指令：${text}`
              : text
          }
        ],
        max_tokens: 1000,
        temperature: 0.7
      };

      const response = await this.httpClient.post(
        `/openai/deployments/${this.config.model}/chat/completions?api-version=2024-10-01-preview`,
        requestData
      );

      return this.parseResponse(response.data, text);
    } catch (error) {
      throw this.handleAPIError(error);
    }
  }

  /**
   * 獲取系統提示詞
   */
  private getSystemPrompt(): string {
    return `你是 SpeechPilot AI 助手，專門處理語音輸入指令。你的任務是：

1. 理解用戶的語音指令或文字指令
2. 根據指令類型提供適當的回應：
   - 寫作任務：直接提供所需內容（如文章、郵件、社交媒體貼文等）
   - 問題回答：提供準確、簡潔的答案
   - 翻譯任務：提供準確的翻譯
   - 格式化任務：按要求格式化內容
   - 其他指令：執行相應操作

3. 回應原則：
   - 直接提供可用的內容，不需要額外說明
   - 保持簡潔但完整
   - 使用繁體中文回應（除非特別要求其他語言）
   - 如果指令不清楚，提供最合理的解釋

4. 特殊處理：
   - 如果是創作任務，直接提供創作內容
   - 如果是技術問題，提供實用的解決方案
   - 如果是日常對話，提供友善的回應

請根據用戶的指令提供最適合直接輸入到文字欄位的內容。`;
  }

  /**
   * 解析 API 回應
   */
  private parseResponse(responseData: any, originalText?: string): AIProcessResult {
    try {
      const choice = responseData.choices?.[0];
      if (!choice) {
        throw new Error('無效的 API 回應格式');
      }

      const processedText = choice.message?.content || '';
      
      // 嘗試識別意圖
      const intent = this.detectIntent(originalText || processedText);

      return {
        originalText: originalText || '語音輸入',
        processedText: processedText.trim(),
        intent
      };
    } catch (error) {
      throw createError(
        `解析 AI 回應失敗: ${(error as Error).message}`,
        ERROR_CODES.OPENAI_SERVICE_ERROR,
        error
      );
    }
  }

  /**
   * 檢測用戶意圖
   */
  private detectIntent(text: string): string {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('寫') || lowerText.includes('文章') || lowerText.includes('內容')) {
      return 'writing';
    } else if (lowerText.includes('翻譯') || lowerText.includes('translate')) {
      return 'translation';
    } else if (lowerText.includes('問') || lowerText.includes('什麼') || lowerText.includes('?')) {
      return 'question';
    } else if (lowerText.includes('格式') || lowerText.includes('整理')) {
      return 'formatting';
    } else {
      return 'general';
    }
  }

  /**
   * 處理 API 錯誤
   */
  private handleAPIError(error: any): Error {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const message = error.response?.data?.error?.message || error.message;
      
      switch (status) {
        case 401:
          return createError('API 金鑰無效', ERROR_CODES.OPENAI_SERVICE_ERROR, error);
        case 403:
          return createError('API 權限不足', ERROR_CODES.OPENAI_SERVICE_ERROR, error);
        case 429:
          return createError('API 請求頻率超限', ERROR_CODES.OPENAI_SERVICE_ERROR, error);
        case 500:
          return createError('OpenAI 服務器錯誤', ERROR_CODES.OPENAI_SERVICE_ERROR, error);
        default:
          return createError(
            `OpenAI API 錯誤: ${message}`,
            ERROR_CODES.OPENAI_SERVICE_ERROR,
            error
          );
      }
    }
    
    return createError(
      `OpenAI 服務錯誤: ${(error as Error).message}`,
      ERROR_CODES.OPENAI_SERVICE_ERROR,
      error
    );
  }

  /**
   * 銷毀服務
   */
  destroy(): void {
    this.httpClient = null;
    this.config = null;
    this.removeAllListeners();
  }
}
