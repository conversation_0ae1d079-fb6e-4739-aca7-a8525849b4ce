import { EventEmitter } from 'events';
import { AUDIO_CONFIG, ERROR_CODES } from '../../shared/constants';
import { createError, isValidAudioBlob } from '../../shared/utils';

export class AudioRecorder extends EventEmitter {
  private mediaRecorder: MediaRecorder | null = null;
  private audioStream: MediaStream | null = null;
  private audioChunks: Blob[] = [];
  private isRecording = false;
  private recordingStartTime = 0;
  private silenceDetectionTimer: NodeJS.Timeout | null = null;

  constructor() {
    super();
  }

  /**
   * 開始錄音
   */
  async startRecording(deviceId?: string): Promise<void> {
    if (this.isRecording) {
      throw createError('錄音已在進行中', ERROR_CODES.RECORDING_FAILED);
    }

    try {
      // 請求音頻權限並獲取音頻流
      this.audioStream = await this.getAudioStream(deviceId);
      
      // 檢查瀏覽器支援
      if (!MediaRecorder.isTypeSupported('audio/webm')) {
        throw createError('瀏覽器不支援音頻錄製', ERROR_CODES.RECORDING_FAILED);
      }

      // 創建 MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: 'audio/webm',
        audioBitsPerSecond: AUDIO_CONFIG.sampleRate * AUDIO_CONFIG.bitsPerSample
      });

      // 設置事件監聽器
      this.setupMediaRecorderEvents();

      // 重置音頻塊
      this.audioChunks = [];
      
      // 開始錄音
      this.mediaRecorder.start();
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      // 設置最大錄音時間
      setTimeout(() => {
        if (this.isRecording) {
          this.stopRecording();
        }
      }, AUDIO_CONFIG.maxRecordingTime);

      // 開始靜音檢測
      this.startSilenceDetection();

      this.emit('recording-started');
    } catch (error) {
      await this.cleanup();
      throw createError(
        `錄音啟動失敗: ${(error as Error).message}`,
        ERROR_CODES.RECORDING_FAILED,
        error
      );
    }
  }

  /**
   * 停止錄音
   */
  async stopRecording(): Promise<Blob> {
    if (!this.isRecording || !this.mediaRecorder) {
      throw createError('沒有正在進行的錄音', ERROR_CODES.RECORDING_FAILED);
    }

    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(createError('MediaRecorder 不存在', ERROR_CODES.RECORDING_FAILED));
        return;
      }

      // 設置停止事件監聽器
      this.mediaRecorder.onstop = async () => {
        try {
          const audioBlob = await this.createAudioBlob();
          await this.cleanup();
          
          if (!isValidAudioBlob(audioBlob)) {
            reject(createError('錄音數據無效', ERROR_CODES.RECORDING_FAILED));
            return;
          }

          this.emit('recording-stopped', audioBlob);
          resolve(audioBlob);
        } catch (error) {
          await this.cleanup();
          reject(error);
        }
      };

      // 停止錄音
      this.mediaRecorder.stop();
      this.isRecording = false;
      
      // 清除靜音檢測
      if (this.silenceDetectionTimer) {
        clearTimeout(this.silenceDetectionTimer);
        this.silenceDetectionTimer = null;
      }
    });
  }

  /**
   * 獲取可用的音頻設備
   */
  async getAudioDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audioinput');
    } catch (error) {
      throw createError(
        `獲取音頻設備失敗: ${(error as Error).message}`,
        ERROR_CODES.AUDIO_DEVICE_NOT_FOUND,
        error
      );
    }
  }

  /**
   * 檢查是否正在錄音
   */
  getIsRecording(): boolean {
    return this.isRecording;
  }

  /**
   * 獲取錄音時長
   */
  getRecordingDuration(): number {
    if (!this.isRecording) return 0;
    return Date.now() - this.recordingStartTime;
  }

  /**
   * 獲取音頻流
   */
  private async getAudioStream(deviceId?: string): Promise<MediaStream> {
    try {
      const constraints: MediaStreamConstraints = {
        audio: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          sampleRate: AUDIO_CONFIG.sampleRate,
          channelCount: AUDIO_CONFIG.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      };

      return await navigator.mediaDevices.getUserMedia(constraints);
    } catch (error) {
      if ((error as any).name === 'NotAllowedError') {
        throw createError('音頻權限被拒絕', ERROR_CODES.AUDIO_PERMISSION_DENIED, error);
      } else if ((error as any).name === 'NotFoundError') {
        throw createError('找不到音頻設備', ERROR_CODES.AUDIO_DEVICE_NOT_FOUND, error);
      } else {
        throw createError(
          `獲取音頻流失敗: ${(error as Error).message}`,
          ERROR_CODES.RECORDING_FAILED,
          error
        );
      }
    }
  }

  /**
   * 設置 MediaRecorder 事件監聽器
   */
  private setupMediaRecorderEvents(): void {
    if (!this.mediaRecorder) return;

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data);
      }
    };

    this.mediaRecorder.onerror = (event) => {
      this.emit('recording-error', event.error);
    };
  }

  /**
   * 創建音頻 Blob
   */
  private async createAudioBlob(): Promise<Blob> {
    if (this.audioChunks.length === 0) {
      throw createError('沒有音頻數據', ERROR_CODES.RECORDING_FAILED);
    }

    return new Blob(this.audioChunks, { type: 'audio/webm' });
  }

  /**
   * 開始靜音檢測
   */
  private startSilenceDetection(): void {
    if (!this.audioStream) return;

    try {
      const audioContext = new AudioContext();
      const source = audioContext.createMediaStreamSource(this.audioStream);
      const analyser = audioContext.createAnalyser();
      
      analyser.fftSize = 256;
      source.connect(analyser);

      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      let silenceStart = 0;
      let lastSoundTime = Date.now();

      const checkSilence = () => {
        if (!this.isRecording) return;

        analyser.getByteFrequencyData(dataArray);
        
        // 計算音量
        const volume = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength / 255;

        if (volume > AUDIO_CONFIG.silenceThreshold) {
          lastSoundTime = Date.now();
          silenceStart = 0;
        } else {
          if (silenceStart === 0) {
            silenceStart = Date.now();
          }
          
          // 檢查是否超過靜音時間閾值
          if (Date.now() - silenceStart > AUDIO_CONFIG.silenceTimeout) {
            this.emit('silence-detected');
            // 可以選擇自動停止錄音
            // this.stopRecording();
            return;
          }
        }

        this.silenceDetectionTimer = setTimeout(checkSilence, 100);
      };

      checkSilence();
    } catch (error) {
      console.warn('靜音檢測啟動失敗:', error);
    }
  }

  /**
   * 清理資源
   */
  private async cleanup(): Promise<void> {
    this.isRecording = false;
    
    if (this.silenceDetectionTimer) {
      clearTimeout(this.silenceDetectionTimer);
      this.silenceDetectionTimer = null;
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }

    this.mediaRecorder = null;
    this.audioChunks = [];
  }
}
