import { dialog, Notification, BrowserWindow } from 'electron';
import { EventEmitter } from 'events';
import { SpeechPilotError } from '../../shared/types';
import { ERROR_CODES } from '../../shared/constants';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { app } from 'electron';

interface ErrorLog {
  timestamp: string;
  level: 'error' | 'warning' | 'info';
  code: string;
  message: string;
  details?: any;
  stack?: string;
}

export class ErrorHandler extends EventEmitter {
  private logFilePath: string;
  private errorLogs: ErrorLog[] = [];
  private maxLogEntries = 1000;
  private showNotifications = true;

  constructor() {
    super();
    this.setupLogFile();
    this.setupGlobalErrorHandlers();
  }

  /**
   * 設置日誌檔案
   */
  private setupLogFile(): void {
    const userDataPath = app.getPath('userData');
    const logsDir = join(userDataPath, 'logs');
    
    if (!existsSync(logsDir)) {
      mkdirSync(logsDir, { recursive: true });
    }
    
    const today = new Date().toISOString().split('T')[0];
    this.logFilePath = join(logsDir, `speechpilot-${today}.log`);
  }

  /**
   * 設置全域錯誤處理器
   */
  private setupGlobalErrorHandlers(): void {
    // 處理未捕獲的異常
    process.on('uncaughtException', (error) => {
      this.handleError(error, 'error', 'UNCAUGHT_EXCEPTION');
    });

    // 處理未處理的 Promise 拒絕
    process.on('unhandledRejection', (reason, promise) => {
      this.handleError(
        new Error(`Unhandled Promise Rejection: ${reason}`),
        'error',
        'UNHANDLED_REJECTION',
        { promise, reason }
      );
    });
  }

  /**
   * 處理錯誤
   */
  handleError(
    error: Error | SpeechPilotError,
    level: 'error' | 'warning' | 'info' = 'error',
    code?: string,
    details?: any
  ): void {
    const errorCode = code || (error as SpeechPilotError).code || 'UNKNOWN_ERROR';
    const errorMessage = error.message || '未知錯誤';
    
    // 創建錯誤日誌條目
    const logEntry: ErrorLog = {
      timestamp: new Date().toISOString(),
      level,
      code: errorCode,
      message: errorMessage,
      details,
      stack: error.stack
    };

    // 記錄到內存
    this.errorLogs.push(logEntry);
    if (this.errorLogs.length > this.maxLogEntries) {
      this.errorLogs.shift();
    }

    // 寫入日誌檔案
    this.writeToLogFile(logEntry);

    // 顯示通知
    if (this.showNotifications && level === 'error') {
      this.showErrorNotification(errorMessage, errorCode);
    }

    // 發出事件
    this.emit('error-logged', logEntry);

    // 根據錯誤類型決定是否顯示對話框
    if (level === 'error' && this.shouldShowDialog(errorCode)) {
      this.showErrorDialog(errorMessage, errorCode, details);
    }

    // 控制台輸出
    console.error(`[${level.toUpperCase()}] ${errorCode}: ${errorMessage}`, details);
  }

  /**
   * 寫入日誌檔案
   */
  private writeToLogFile(logEntry: ErrorLog): void {
    try {
      const logLine = `${logEntry.timestamp} [${logEntry.level.toUpperCase()}] ${logEntry.code}: ${logEntry.message}\n`;
      writeFileSync(this.logFilePath, logLine, { flag: 'a' });
    } catch (error) {
      console.error('寫入日誌檔案失敗:', error);
    }
  }

  /**
   * 顯示錯誤通知
   */
  private showErrorNotification(message: string, code: string): void {
    try {
      const notification = new Notification({
        title: 'SpeechPilot 錯誤',
        body: this.getUserFriendlyMessage(message, code),
        icon: this.getNotificationIcon(),
        urgency: 'critical'
      });

      notification.show();
      
      notification.on('click', () => {
        this.showMainWindow();
      });
    } catch (error) {
      console.error('顯示通知失敗:', error);
    }
  }

  /**
   * 顯示錯誤對話框
   */
  private async showErrorDialog(
    message: string,
    code: string,
    details?: any
  ): Promise<void> {
    try {
      const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
      
      const options = {
        type: 'error' as const,
        title: 'SpeechPilot 錯誤',
        message: this.getUserFriendlyMessage(message, code),
        detail: this.getErrorSolution(code),
        buttons: ['確定', '查看詳細資訊', '回報問題'],
        defaultId: 0,
        cancelId: 0
      };

      const result = await dialog.showMessageBox(mainWindow, options);
      
      if (result.response === 1) {
        // 顯示詳細資訊
        this.showDetailedError(message, code, details);
      } else if (result.response === 2) {
        // 回報問題
        this.reportIssue(message, code, details);
      }
    } catch (error) {
      console.error('顯示錯誤對話框失敗:', error);
    }
  }

  /**
   * 顯示詳細錯誤資訊
   */
  private async showDetailedError(
    message: string,
    code: string,
    details?: any
  ): Promise<void> {
    const detailText = `
錯誤代碼: ${code}
錯誤訊息: ${message}
時間: ${new Date().toLocaleString()}
詳細資訊: ${JSON.stringify(details, null, 2)}
    `.trim();

    await dialog.showMessageBox(BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0], {
      type: 'info',
      title: '錯誤詳細資訊',
      message: '錯誤詳細資訊',
      detail: detailText,
      buttons: ['確定', '複製到剪貼簿'],
      defaultId: 0
    });
  }

  /**
   * 獲取用戶友好的錯誤訊息
   */
  private getUserFriendlyMessage(message: string, code: string): string {
    const friendlyMessages: Record<string, string> = {
      [ERROR_CODES.AUDIO_PERMISSION_DENIED]: '無法存取麥克風，請檢查權限設定',
      [ERROR_CODES.AUDIO_DEVICE_NOT_FOUND]: '找不到音頻設備，請檢查麥克風連接',
      [ERROR_CODES.RECORDING_FAILED]: '錄音失敗，請重試',
      [ERROR_CODES.SPEECH_SERVICE_ERROR]: 'Azure 語音服務錯誤，請檢查網路連接和 API 設定',
      [ERROR_CODES.OPENAI_SERVICE_ERROR]: 'Azure OpenAI 服務錯誤，請檢查 API 設定',
      [ERROR_CODES.TEXT_INPUT_FAILED]: '文字輸入失敗，請確保目標應用程式處於焦點狀態',
      [ERROR_CODES.CONFIG_LOAD_FAILED]: '載入配置失敗，將使用預設設定',
      [ERROR_CODES.CONFIG_SAVE_FAILED]: '儲存配置失敗，請檢查檔案權限',
      [ERROR_CODES.HOTKEY_REGISTRATION_FAILED]: '快捷鍵註冊失敗，可能與其他應用程式衝突'
    };

    return friendlyMessages[code] || message;
  }

  /**
   * 獲取錯誤解決方案
   */
  private getErrorSolution(code: string): string {
    const solutions: Record<string, string> = {
      [ERROR_CODES.AUDIO_PERMISSION_DENIED]: '請在系統設定中允許 SpeechPilot 存取麥克風',
      [ERROR_CODES.AUDIO_DEVICE_NOT_FOUND]: '請檢查麥克風是否正確連接並在系統中可見',
      [ERROR_CODES.RECORDING_FAILED]: '請重新啟動應用程式或檢查音頻設備',
      [ERROR_CODES.SPEECH_SERVICE_ERROR]: '請檢查網路連接和 Azure Speech Service API 金鑰',
      [ERROR_CODES.OPENAI_SERVICE_ERROR]: '請檢查 Azure OpenAI API 金鑰和端點設定',
      [ERROR_CODES.TEXT_INPUT_FAILED]: '請確保目標應用程式的文字欄位處於焦點狀態',
      [ERROR_CODES.CONFIG_LOAD_FAILED]: '請檢查配置檔案是否存在且格式正確',
      [ERROR_CODES.CONFIG_SAVE_FAILED]: '請檢查應用程式是否有寫入權限',
      [ERROR_CODES.HOTKEY_REGISTRATION_FAILED]: '請嘗試更改快捷鍵組合或關閉衝突的應用程式'
    };

    return solutions[code] || '請聯繫技術支援獲取協助';
  }

  /**
   * 判斷是否應該顯示對話框
   */
  private shouldShowDialog(code: string): boolean {
    const criticalErrors = [
      ERROR_CODES.AUDIO_PERMISSION_DENIED,
      ERROR_CODES.CONFIG_LOAD_FAILED,
      ERROR_CODES.HOTKEY_REGISTRATION_FAILED
    ];

    return criticalErrors.includes(code);
  }

  /**
   * 獲取通知圖示
   */
  private getNotificationIcon(): string {
    return join(__dirname, '../../../assets/tray-icon.png');
  }

  /**
   * 顯示主視窗
   */
  private showMainWindow(): void {
    const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
    if (mainWindow) {
      mainWindow.show();
      mainWindow.focus();
    }
  }

  /**
   * 回報問題
   */
  private reportIssue(message: string, code: string, details?: any): void {
    // 這裡可以實現自動回報功能，例如發送到錯誤追蹤服務
    console.log('回報問題:', { message, code, details });
    this.emit('issue-reported', { message, code, details });
  }

  /**
   * 記錄資訊
   */
  logInfo(message: string, details?: any): void {
    this.handleError(new Error(message), 'info', 'INFO', details);
  }

  /**
   * 記錄警告
   */
  logWarning(message: string, details?: any): void {
    this.handleError(new Error(message), 'warning', 'WARNING', details);
  }

  /**
   * 獲取錯誤日誌
   */
  getErrorLogs(level?: 'error' | 'warning' | 'info'): ErrorLog[] {
    if (level) {
      return this.errorLogs.filter(log => log.level === level);
    }
    return [...this.errorLogs];
  }

  /**
   * 清除錯誤日誌
   */
  clearLogs(): void {
    this.errorLogs = [];
    this.emit('logs-cleared');
  }

  /**
   * 設置通知開關
   */
  setNotificationsEnabled(enabled: boolean): void {
    this.showNotifications = enabled;
  }

  /**
   * 匯出錯誤日誌
   */
  exportLogs(): string {
    return JSON.stringify(this.errorLogs, null, 2);
  }

  /**
   * 銷毀錯誤處理器
   */
  destroy(): void {
    this.removeAllListeners();
  }
}
