// 語音模式類型
export enum SpeechMode {
  AI_SPEECH_TO_TEXT = 'ai_speech_to_text',
  DIRECT_SPEECH_TO_TEXT = 'direct_speech_to_text'
}

// 錄音狀態
export enum RecordingState {
  IDLE = 'idle',
  RECORDING = 'recording',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// 快捷鍵配置
export interface HotkeyConfig {
  aiSpeechToText: string;
  directSpeechToText: string;
  mode: 'press' | 'toggle'; // 按著或按一下
}

// Azure 配置
export interface AzureConfig {
  openai: {
    endpoint: string;
    apiKey: string;
    model: string;
  };
  speech: {
    region: string;
    apiKey: string;
    language: string;
  };
}

// 音頻設備配置
export interface AudioDeviceConfig {
  deviceId: string;
  label: string;
}

// 應用程式配置
export interface AppConfig {
  hotkeys: HotkeyConfig;
  azure: AzureConfig;
  audioDevice: AudioDeviceConfig;
  autoStart: boolean;
  minimizeToTray: boolean;
}

// 錄音會話資料
export interface RecordingSession {
  id: string;
  mode: SpeechMode;
  state: RecordingState;
  startTime: number;
  endTime?: number;
  audioData?: Blob;
  transcription?: string;
  aiResponse?: string;
  error?: string;
}

// IPC 事件類型
export interface IPCEvents {
  // 主程序到渲染程序
  'recording-state-changed': RecordingSession;
  'config-updated': AppConfig;
  'error-occurred': { message: string; details?: any };
  
  // 渲染程序到主程序
  'start-recording': { mode: SpeechMode };
  'stop-recording': void;
  'get-config': void;
  'update-config': Partial<AppConfig>;
  'get-audio-devices': void;
  'minimize-to-tray': void;
  'show-window': void;
}

// API 回應類型
export interface SpeechToTextResult {
  text: string;
  confidence: number;
}

export interface AIProcessResult {
  originalText: string;
  processedText: string;
  intent: string;
}

// 錯誤類型
export class SpeechPilotError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'SpeechPilotError';
  }
}
