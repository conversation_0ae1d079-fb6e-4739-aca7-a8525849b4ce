<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SpeechPilot - AI語音助手</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    .container {
      text-align: center;
      max-width: 400px;
      width: 100%;
    }
    
    .logo {
      font-size: 3rem;
      margin-bottom: 1rem;
    }
    
    .title {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    
    .subtitle {
      font-size: 1rem;
      opacity: 0.8;
      margin-bottom: 2rem;
    }
    
    .status {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 10px;
      padding: 1rem;
      margin-bottom: 2rem;
      backdrop-filter: blur(10px);
    }
    
    .buttons {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 8px;
      padding: 1rem 2rem;
      color: white;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }
    
    .btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
    
    .btn:active {
      transform: translateY(0);
    }
    
    .btn.primary {
      background: rgba(255, 255, 255, 0.3);
      font-weight: 600;
    }
    
    .hotkeys {
      margin-top: 2rem;
      font-size: 0.9rem;
      opacity: 0.7;
    }
    
    .hotkey {
      background: rgba(255, 255, 255, 0.1);
      padding: 0.2rem 0.5rem;
      border-radius: 4px;
      margin: 0 0.2rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">🎤</div>
    <h1 class="title">SpeechPilot</h1>
    <p class="subtitle">AI語音助手 - Speech in, text out</p>
    
    <div class="status">
      <div id="status-text">準備就緒</div>
      <div id="config-status">正在檢查配置...</div>
    </div>
    
    <div class="buttons">
      <button class="btn primary" id="ai-speech-btn">
        🤖 AI 語音輸入
      </button>
      <button class="btn" id="direct-speech-btn">
        📝 直接語音輸入
      </button>
      <button class="btn" id="settings-btn">
        ⚙️ 設定
      </button>
    </div>
    
    <div class="hotkeys">
      <div>快捷鍵：</div>
      <div>
        AI 語音輸入：<span class="hotkey" id="ai-hotkey">Ctrl+Shift+C</span>
      </div>
      <div>
        直接語音輸入：<span class="hotkey" id="direct-hotkey">Ctrl+Shift+V</span>
      </div>
    </div>
  </div>

  <script>
    // 基本的狀態管理和事件處理
    let currentConfig = null;
    
    // DOM 元素
    const statusText = document.getElementById('status-text');
    const configStatus = document.getElementById('config-status');
    const aiSpeechBtn = document.getElementById('ai-speech-btn');
    const directSpeechBtn = document.getElementById('direct-speech-btn');
    const settingsBtn = document.getElementById('settings-btn');
    const aiHotkey = document.getElementById('ai-hotkey');
    const directHotkey = document.getElementById('direct-hotkey');
    
    // 檢查 Electron API 是否可用
    if (window.electronAPI) {
      initializeApp();
    } else {
      configStatus.textContent = '錯誤：無法連接到主程序';
    }
    
    async function initializeApp() {
      try {
        // 載入配置
        currentConfig = await window.electronAPI.getConfig();
        updateUI();
        
        // 設置事件監聽器
        setupEventListeners();
        
        configStatus.textContent = '配置載入完成';
      } catch (error) {
        configStatus.textContent = '配置載入失敗：' + error.message;
      }
    }
    
    function updateUI() {
      if (currentConfig) {
        // 更新快捷鍵顯示
        aiHotkey.textContent = currentConfig.hotkeys.aiSpeechToText;
        directHotkey.textContent = currentConfig.hotkeys.directSpeechToText;
        
        // 檢查配置完整性
        const missingItems = getMissingConfigItems();
        if (missingItems.length > 0) {
          configStatus.textContent = '配置不完整：' + missingItems.join(', ');
          statusText.textContent = '請完成設定';
        } else {
          configStatus.textContent = '配置完整';
          statusText.textContent = '準備就緒';
        }
      }
    }
    
    function getMissingConfigItems() {
      const missing = [];
      if (!currentConfig.azure.openai.endpoint) missing.push('OpenAI Endpoint');
      if (!currentConfig.azure.openai.apiKey) missing.push('OpenAI API Key');
      if (!currentConfig.azure.speech.region) missing.push('Speech Region');
      if (!currentConfig.azure.speech.apiKey) missing.push('Speech API Key');
      return missing;
    }
    
    function setupEventListeners() {
      // 按鈕事件
      aiSpeechBtn.addEventListener('click', () => {
        window.electronAPI.startRecording('ai_speech_to_text');
      });
      
      directSpeechBtn.addEventListener('click', () => {
        window.electronAPI.startRecording('direct_speech_to_text');
      });
      
      settingsBtn.addEventListener('click', () => {
        // TODO: 開啟設定視窗
        alert('設定功能即將推出');
      });
      
      // 監聽錄音狀態變化
      window.electronAPI.onRecordingStateChanged((session) => {
        updateRecordingStatus(session);
      });
      
      // 監聽配置更新
      window.electronAPI.onConfigUpdated((config) => {
        currentConfig = config;
        updateUI();
      });
      
      // 監聽錯誤
      window.electronAPI.onErrorOccurred((error) => {
        statusText.textContent = '錯誤：' + error.message;
        setTimeout(() => {
          statusText.textContent = '準備就緒';
        }, 3000);
      });
    }
    
    function updateRecordingStatus(session) {
      switch (session.state) {
        case 'recording':
          statusText.textContent = '正在錄音...';
          break;
        case 'processing':
          statusText.textContent = '正在處理...';
          break;
        case 'completed':
          statusText.textContent = '完成！';
          setTimeout(() => {
            statusText.textContent = '準備就緒';
          }, 2000);
          break;
        case 'error':
          statusText.textContent = '錯誤：' + session.error;
          setTimeout(() => {
            statusText.textContent = '準備就緒';
          }, 3000);
          break;
      }
    }
  </script>
</body>
</html>
