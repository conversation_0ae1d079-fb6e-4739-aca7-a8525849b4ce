import { SpeechPilotError } from '../types';

// 生成唯一 ID
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 格式化時間
export function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `${remainingSeconds}s`;
}

// 延遲函數
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 重試函數
export async function retry<T>(
  fn: () => Promise<T>,
  attempts: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i < attempts; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      if (i < attempts - 1) {
        await delay(delayMs);
      }
    }
  }
  
  throw lastError!;
}

// 驗證配置
export function validateConfig(config: any): boolean {
  if (!config) return false;
  
  // 檢查必要的配置項目
  const requiredPaths = [
    'azure.openai.endpoint',
    'azure.openai.apiKey',
    'azure.speech.region',
    'azure.speech.apiKey',
    'hotkeys.aiSpeechToText',
    'hotkeys.directSpeechToText'
  ];
  
  for (const path of requiredPaths) {
    if (!getNestedValue(config, path)) {
      return false;
    }
  }
  
  return true;
}

// 獲取嵌套物件值
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// 創建錯誤
export function createError(
  message: string,
  code: string,
  details?: any
): SpeechPilotError {
  return new SpeechPilotError(message, code, details);
}

// 安全的 JSON 解析
export function safeJsonParse<T>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return defaultValue;
  }
}

// 檢查是否為有效的音頻 Blob
export function isValidAudioBlob(blob: Blob): boolean {
  return blob instanceof Blob && 
         blob.size > 0 && 
         blob.type.startsWith('audio/');
}

// 將 Blob 轉換為 ArrayBuffer
export function blobToArrayBuffer(blob: Blob): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = reject;
    reader.readAsArrayBuffer(blob);
  });
}

// 檢查快捷鍵格式
export function isValidHotkey(hotkey: string): boolean {
  const validModifiers = ['CommandOrControl', 'Command', 'Control', 'Alt', 'Shift'];
  const parts = hotkey.split('+');
  
  if (parts.length < 2) return false;
  
  const modifiers = parts.slice(0, -1);
  const key = parts[parts.length - 1];
  
  // 檢查修飾鍵是否有效
  for (const modifier of modifiers) {
    if (!validModifiers.includes(modifier)) {
      return false;
    }
  }
  
  // 檢查按鍵是否有效（簡單檢查）
  return key.length === 1 || ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'].includes(key);
}
