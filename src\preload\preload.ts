import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron';
import { SpeechMode, AppConfig, RecordingSession } from '../shared/types';

// 定義暴露給渲染程序的 API
const electronAPI = {
  // 配置相關
  getConfig: (): Promise<AppConfig> => ipcRenderer.invoke('get-config'),
  updateConfig: (config: Partial<AppConfig>): Promise<void> => 
    ipcRenderer.invoke('update-config', config),

  // 錄音相關
  startRecording: (mode: SpeechMode): Promise<void> => 
    ipcRenderer.invoke('start-recording', { mode }),
  stopRecording: (): Promise<void> => ipcRenderer.invoke('stop-recording'),

  // 音頻設備
  getAudioDevices: (): Promise<MediaDeviceInfo[]> => 
    ipcRenderer.invoke('get-audio-devices'),

  // 視窗控制
  minimizeToTray: (): Promise<void> => ipcRenderer.invoke('minimize-to-tray'),
  showWindow: (): Promise<void> => ipcRenderer.invoke('show-window'),

  // 事件監聽
  onRecordingStateChanged: (callback: (session: RecordingSession) => void) => {
    ipcRenderer.on('recording-state-changed', (_, session) => callback(session));
    return () => ipcRenderer.removeAllListeners('recording-state-changed');
  },

  onConfigUpdated: (callback: (config: AppConfig) => void) => {
    ipcRenderer.on('config-updated', (_, config) => callback(config));
    return () => ipcRenderer.removeAllListeners('config-updated');
  },

  onErrorOccurred: (callback: (error: { message: string; details?: any }) => void) => {
    ipcRenderer.on('error-occurred', (_, error) => callback(error));
    return () => ipcRenderer.removeAllListeners('error-occurred');
  },

  // 移除所有監聽器
  removeAllListeners: () => {
    ipcRenderer.removeAllListeners('recording-state-changed');
    ipcRenderer.removeAllListeners('config-updated');
    ipcRenderer.removeAllListeners('error-occurred');
  }
};

// 暴露 API 到渲染程序
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 類型定義（供 TypeScript 使用）
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}
