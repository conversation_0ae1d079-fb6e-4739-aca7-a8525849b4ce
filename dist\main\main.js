"use strict";const u=require("electron"),f=require("path"),q=require("electron-store"),Z=require("dotenv"),J=require("microsoft-cognitiveservices-speech-sdk"),F=require("axios"),h=require("@nut-tree-fork/nut-js"),b=require("fs");function k(r){const e=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(r){for(const t in r)if(t!=="default"){const i=Object.getOwnPropertyDescriptor(r,t);Object.defineProperty(e,t,i.get?i:{enumerable:!0,get:()=>r[t]})}}return e.default=r,Object.freeze(e)}const Y=k(Z),p=k(J);var L={exports:{}},R=typeof Reflect=="object"?Reflect:null,z=R&&typeof R.apply=="function"?R.apply:function(e,t,i){return Function.prototype.apply.call(e,t,i)},A;R&&typeof R.ownKeys=="function"?A=R.ownKeys:Object.getOwnPropertySymbols?A=function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:A=function(e){return Object.getOwnPropertyNames(e)};function Q(r){console&&console.warn&&console.warn(r)}var K=Number.isNaN||function(e){return e!==e};function d(){d.init.call(this)}L.exports=d;L.exports.once=re;d.EventEmitter=d;d.prototype._events=void 0;d.prototype._eventsCount=0;d.prototype._maxListeners=void 0;var x=10;function O(r){if(typeof r!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof r)}Object.defineProperty(d,"defaultMaxListeners",{enumerable:!0,get:function(){return x},set:function(r){if(typeof r!="number"||r<0||K(r))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+r+".");x=r}});d.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};d.prototype.setMaxListeners=function(e){if(typeof e!="number"||e<0||K(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this};function W(r){return r._maxListeners===void 0?d.defaultMaxListeners:r._maxListeners}d.prototype.getMaxListeners=function(){return W(this)};d.prototype.emit=function(e){for(var t=[],i=1;i<arguments.length;i++)t.push(arguments[i]);var n=e==="error",o=this._events;if(o!==void 0)n=n&&o.error===void 0;else if(!n)return!1;if(n){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var l=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw l.context=a,l}var g=o[e];if(g===void 0)return!1;if(typeof g=="function")z(g,this,t);else for(var v=g.length,D=$(g,v),i=0;i<v;++i)z(D[i],this,t);return!0};function U(r,e,t,i){var n,o,a;if(O(t),o=r._events,o===void 0?(o=r._events=Object.create(null),r._eventsCount=0):(o.newListener!==void 0&&(r.emit("newListener",e,t.listener?t.listener:t),o=r._events),a=o[e]),a===void 0)a=o[e]=t,++r._eventsCount;else if(typeof a=="function"?a=o[e]=i?[t,a]:[a,t]:i?a.unshift(t):a.push(t),n=W(r),n>0&&a.length>n&&!a.warned){a.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=r,l.type=e,l.count=a.length,Q(l)}return r}d.prototype.addListener=function(e,t){return U(this,e,t,!1)};d.prototype.on=d.prototype.addListener;d.prototype.prependListener=function(e,t){return U(this,e,t,!0)};function ee(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function V(r,e,t){var i={fired:!1,wrapFn:void 0,target:r,type:e,listener:t},n=ee.bind(i);return n.listener=t,i.wrapFn=n,n}d.prototype.once=function(e,t){return O(t),this.on(e,V(this,e,t)),this};d.prototype.prependOnceListener=function(e,t){return O(t),this.prependListener(e,V(this,e,t)),this};d.prototype.removeListener=function(e,t){var i,n,o,a,l;if(O(t),n=this._events,n===void 0)return this;if(i=n[e],i===void 0)return this;if(i===t||i.listener===t)--this._eventsCount===0?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,i.listener||t));else if(typeof i!="function"){for(o=-1,a=i.length-1;a>=0;a--)if(i[a]===t||i[a].listener===t){l=i[a].listener,o=a;break}if(o<0)return this;o===0?i.shift():te(i,o),i.length===1&&(n[e]=i[0]),n.removeListener!==void 0&&this.emit("removeListener",e,l||t)}return this};d.prototype.off=d.prototype.removeListener;d.prototype.removeAllListeners=function(e){var t,i,n;if(i=this._events,i===void 0)return this;if(i.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):i[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete i[e]),this;if(arguments.length===0){var o=Object.keys(i),a;for(n=0;n<o.length;++n)a=o[n],a!=="removeListener"&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(t=i[e],typeof t=="function")this.removeListener(e,t);else if(t!==void 0)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this};function G(r,e,t){var i=r._events;if(i===void 0)return[];var n=i[e];return n===void 0?[]:typeof n=="function"?t?[n.listener||n]:[n]:t?ie(n):$(n,n.length)}d.prototype.listeners=function(e){return G(this,e,!0)};d.prototype.rawListeners=function(e){return G(this,e,!1)};d.listenerCount=function(r,e){return typeof r.listenerCount=="function"?r.listenerCount(e):H.call(r,e)};d.prototype.listenerCount=H;function H(r){var e=this._events;if(e!==void 0){var t=e[r];if(typeof t=="function")return 1;if(t!==void 0)return t.length}return 0}d.prototype.eventNames=function(){return this._eventsCount>0?A(this._events):[]};function $(r,e){for(var t=new Array(e),i=0;i<e;++i)t[i]=r[i];return t}function te(r,e){for(;e+1<r.length;e++)r[e]=r[e+1];r.pop()}function ie(r){for(var e=new Array(r.length),t=0;t<e.length;++t)e[t]=r[t].listener||r[t];return e}function re(r,e){return new Promise(function(t,i){function n(a){r.removeListener(e,o),i(a)}function o(){typeof r.removeListener=="function"&&r.removeListener("error",n),t([].slice.call(arguments))}j(r,e,o,{once:!0}),e!=="error"&&ne(r,n,{once:!0})})}function ne(r,e,t){typeof r.on=="function"&&j(r,"error",e,t)}function j(r,e,t,i){if(typeof r.on=="function")i.once?r.once(e,t):r.on(e,t);else if(typeof r.addEventListener=="function")r.addEventListener(e,function n(o){i.once&&r.removeEventListener(e,n),t(o)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof r)}var I=L.exports;const S={hotkeys:{aiSpeechToText:"CommandOrControl+Shift+C",directSpeechToText:"CommandOrControl+Shift+V",mode:"toggle"},azure:{openai:{endpoint:"",apiKey:"",model:"gpt-4o-mini-audio-preview"},speech:{region:"eastus",apiKey:"",language:"zh-TW"}},audioDevice:{deviceId:"default",label:"Default Microphone"},autoStart:!1,minimizeToTray:!0},se={name:"SpeechPilot"},C={main:{width:400,height:600,minWidth:350,minHeight:500,resizable:!0,show:!1,frame:!0,titleBarStyle:"default",webPreferences:{nodeIntegration:!1,contextIsolation:!0,enableRemoteModule:!1,webSecurity:!0}},recording:{width:300,height:200,resizable:!1,frame:!1,alwaysOnTop:!0,skipTaskbar:!0,show:!1,webPreferences:{nodeIntegration:!1,contextIsolation:!0,enableRemoteModule:!1,webSecurity:!0}}},y={sampleRate:16e3,channels:1,bitsPerSample:16,maxRecordingTime:6e4,silenceThreshold:.01,silenceTimeout:3e3},E={timeout:3e4,retryAttempts:3,retryDelay:1e3},s={AUDIO_PERMISSION_DENIED:"AUDIO_PERMISSION_DENIED",AUDIO_DEVICE_NOT_FOUND:"AUDIO_DEVICE_NOT_FOUND",RECORDING_FAILED:"RECORDING_FAILED",SPEECH_SERVICE_ERROR:"SPEECH_SERVICE_ERROR",OPENAI_SERVICE_ERROR:"OPENAI_SERVICE_ERROR",TEXT_INPUT_FAILED:"TEXT_INPUT_FAILED",CONFIG_LOAD_FAILED:"CONFIG_LOAD_FAILED",CONFIG_SAVE_FAILED:"CONFIG_SAVE_FAILED",HOTKEY_REGISTRATION_FAILED:"HOTKEY_REGISTRATION_FAILED"};var m=(r=>(r.AI_SPEECH_TO_TEXT="ai_speech_to_text",r.DIRECT_SPEECH_TO_TEXT="direct_speech_to_text",r))(m||{}),_=(r=>(r.IDLE="idle",r.RECORDING="recording",r.PROCESSING="processing",r.COMPLETED="completed",r.ERROR="error",r))(_||{});class oe extends Error{constructor(e,t,i){super(e),this.code=t,this.details=i,this.name="SpeechPilotError"}}function ae(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}function w(r){return new Promise(e=>setTimeout(e,r))}async function N(r,e=3,t=1e3){let i;for(let n=0;n<e;n++)try{return await r()}catch(o){i=o,n<e-1&&await w(t)}throw i}function M(r){if(!r)return!1;const e=["azure.openai.endpoint","azure.openai.apiKey","azure.speech.region","azure.speech.apiKey","hotkeys.aiSpeechToText","hotkeys.directSpeechToText"];for(const t of e)if(!ce(r,t))return!1;return!0}function ce(r,e){return e.split(".").reduce((t,i)=>t==null?void 0:t[i],r)}function c(r,e,t){return new oe(r,e,t)}function he(r){return r instanceof Blob&&r.size>0&&r.type.startsWith("audio/")}function B(r){return new Promise((e,t)=>{const i=new FileReader;i.onload=()=>e(i.result),i.onerror=t,i.readAsArrayBuffer(r)})}function T(r){const e=["CommandOrControl","Command","Control","Alt","Shift"],t=r.split("+");if(t.length<2)return!1;const i=t.slice(0,-1),n=t[t.length-1];for(const o of i)if(!e.includes(o))return!1;return n.length===1||["F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"].includes(n)}class ue extends I.EventEmitter{constructor(){super(),this.isInitialized=!1,this.store=new q({name:"speechpilot-config",defaults:S,schema:{hotkeys:{type:"object",properties:{aiSpeechToText:{type:"string"},directSpeechToText:{type:"string"},mode:{type:"string",enum:["press","toggle"]}}},azure:{type:"object",properties:{openai:{type:"object",properties:{endpoint:{type:"string"},apiKey:{type:"string"},model:{type:"string"}}},speech:{type:"object",properties:{region:{type:"string"},apiKey:{type:"string"},language:{type:"string"}}}}},audioDevice:{type:"object",properties:{deviceId:{type:"string"},label:{type:"string"}}},autoStart:{type:"boolean"},minimizeToTray:{type:"boolean"}}}),this.config=this.store.store}async initialize(){try{await this.loadEnvironmentVariables(),this.loadStoredConfig(),await this.validateCurrentConfig(),this.isInitialized=!0,this.emit("initialized",this.config)}catch(e){throw c(`配置初始化失敗: ${e.message}`,s.CONFIG_LOAD_FAILED,e)}}async loadEnvironmentVariables(){try{const e=f.join(process.cwd(),".env");Y.config({path:e});const t={};process.env.AZURE_OPENAI_ENDPOINT&&(t.azure={...this.config.azure,openai:{...this.config.azure.openai,endpoint:process.env.AZURE_OPENAI_ENDPOINT,apiKey:process.env.AZURE_OPENAI_API_KEY||this.config.azure.openai.apiKey,model:process.env.AZURE_OPENAI_MODEL||this.config.azure.openai.model}}),process.env.AZURE_SPEECH_SERVICE_REGION&&(t.azure={...t.azure||this.config.azure,speech:{...this.config.azure.speech,region:process.env.AZURE_SPEECH_SERVICE_REGION,apiKey:process.env.AZURE_SPEECH_SERVICE_API_KEY||this.config.azure.speech.apiKey,language:process.env.AZURE_SPEECH_LANGUAGE||this.config.azure.speech.language}}),Object.keys(t).length>0&&(this.config={...this.config,...t})}catch(e){console.warn("載入環境變數失敗:",e)}}loadStoredConfig(){try{const e=this.store.store;this.config={...S,...e}}catch(e){console.warn("載入儲存配置失敗，使用預設配置:",e),this.config={...S}}}async validateCurrentConfig(){M(this.config)||console.warn("配置驗證失敗，某些功能可能無法正常使用"),T(this.config.hotkeys.aiSpeechToText)||console.warn("AI 語音輸入快捷鍵格式無效"),T(this.config.hotkeys.directSpeechToText)||console.warn("直接語音輸入快捷鍵格式無效")}getConfig(){return{...this.config}}async updateConfig(e){if(!this.isInitialized)throw c("配置管理器未初始化",s.CONFIG_SAVE_FAILED);try{const t={...this.config,...e};e.hotkeys&&this.validateHotkeys(e.hotkeys),this.config=t,this.store.store=this.config,this.emit("config-changed",this.config)}catch(t){throw c(`配置更新失敗: ${t.message}`,s.CONFIG_SAVE_FAILED,t)}}async updateHotkeys(e){const t={...this.config.hotkeys,...e};this.validateHotkeys(t),await this.updateConfig({hotkeys:t})}async updateAzureConfig(e){const t={openai:{...this.config.azure.openai,...e.openai},speech:{...this.config.azure.speech,...e.speech}};await this.updateConfig({azure:t})}async updateAudioDevice(e){await this.updateConfig({audioDevice:e})}async resetToDefaults(){try{this.config={...S},this.store.clear(),this.store.store=this.config,this.emit("config-reset",this.config)}catch(e){throw c(`重置配置失敗: ${e.message}`,s.CONFIG_SAVE_FAILED,e)}}exportConfig(){const e={...this.config,azure:{openai:{...this.config.azure.openai,apiKey:"***"},speech:{...this.config.azure.speech,apiKey:"***"}}};return JSON.stringify(e,null,2)}async importConfig(e){try{const t=JSON.parse(e);if(!this.isValidConfigStructure(t))throw new Error("配置格式無效");const i=this.mergeImportedConfig(t);await this.updateConfig(i)}catch(t){throw c(`匯入配置失敗: ${t.message}`,s.CONFIG_LOAD_FAILED,t)}}validateHotkeys(e){if(e.aiSpeechToText&&!T(e.aiSpeechToText))throw c("AI 語音輸入快捷鍵格式無效",s.CONFIG_SAVE_FAILED);if(e.directSpeechToText&&!T(e.directSpeechToText))throw c("直接語音輸入快捷鍵格式無效",s.CONFIG_SAVE_FAILED);if(e.aiSpeechToText&&e.directSpeechToText&&e.aiSpeechToText===e.directSpeechToText)throw c("快捷鍵不能重複",s.CONFIG_SAVE_FAILED)}isValidConfigStructure(e){return e&&typeof e=="object"&&e.hotkeys&&e.azure&&e.audioDevice}mergeImportedConfig(e){const t={};return e.hotkeys&&(t.hotkeys={...this.config.hotkeys,...e.hotkeys}),e.azure&&(t.azure={openai:{...this.config.azure.openai,...e.azure.openai,apiKey:e.azure.openai.apiKey==="***"?this.config.azure.openai.apiKey:e.azure.openai.apiKey},speech:{...this.config.azure.speech,...e.azure.speech,apiKey:e.azure.speech.apiKey==="***"?this.config.azure.speech.apiKey:e.azure.speech.apiKey}}),e.audioDevice&&(t.audioDevice=e.audioDevice),typeof e.autoStart=="boolean"&&(t.autoStart=e.autoStart),typeof e.minimizeToTray=="boolean"&&(t.minimizeToTray=e.minimizeToTray),t}isConfigComplete(){return M(this.config)}getMissingConfigItems(){const e=[];return this.config.azure.openai.endpoint||e.push("Azure OpenAI Endpoint"),this.config.azure.openai.apiKey||e.push("Azure OpenAI API Key"),this.config.azure.speech.region||e.push("Azure Speech Region"),this.config.azure.speech.apiKey||e.push("Azure Speech API Key"),e}destroy(){this.removeAllListeners()}}class le extends I.EventEmitter{constructor(){super(),this.mediaRecorder=null,this.audioStream=null,this.audioChunks=[],this.isRecording=!1,this.recordingStartTime=0,this.silenceDetectionTimer=null}async startRecording(e){if(this.isRecording)throw c("錄音已在進行中",s.RECORDING_FAILED);try{if(this.audioStream=await this.getAudioStream(e),!MediaRecorder.isTypeSupported("audio/webm"))throw c("瀏覽器不支援音頻錄製",s.RECORDING_FAILED);this.mediaRecorder=new MediaRecorder(this.audioStream,{mimeType:"audio/webm",audioBitsPerSecond:y.sampleRate*y.bitsPerSample}),this.setupMediaRecorderEvents(),this.audioChunks=[],this.mediaRecorder.start(),this.isRecording=!0,this.recordingStartTime=Date.now(),setTimeout(()=>{this.isRecording&&this.stopRecording()},y.maxRecordingTime),this.startSilenceDetection(),this.emit("recording-started")}catch(t){throw await this.cleanup(),c(`錄音啟動失敗: ${t.message}`,s.RECORDING_FAILED,t)}}async stopRecording(){if(!this.isRecording||!this.mediaRecorder)throw c("沒有正在進行的錄音",s.RECORDING_FAILED);return new Promise((e,t)=>{if(!this.mediaRecorder){t(c("MediaRecorder 不存在",s.RECORDING_FAILED));return}this.mediaRecorder.onstop=async()=>{try{const i=await this.createAudioBlob();if(await this.cleanup(),!he(i)){t(c("錄音數據無效",s.RECORDING_FAILED));return}this.emit("recording-stopped",i),e(i)}catch(i){await this.cleanup(),t(i)}},this.mediaRecorder.stop(),this.isRecording=!1,this.silenceDetectionTimer&&(clearTimeout(this.silenceDetectionTimer),this.silenceDetectionTimer=null)})}async getAudioDevices(){try{return(await navigator.mediaDevices.enumerateDevices()).filter(t=>t.kind==="audioinput")}catch(e){throw c(`獲取音頻設備失敗: ${e.message}`,s.AUDIO_DEVICE_NOT_FOUND,e)}}getIsRecording(){return this.isRecording}getRecordingDuration(){return this.isRecording?Date.now()-this.recordingStartTime:0}async getAudioStream(e){try{const t={audio:{deviceId:e?{exact:e}:void 0,sampleRate:y.sampleRate,channelCount:y.channels,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}};return await navigator.mediaDevices.getUserMedia(t)}catch(t){throw t.name==="NotAllowedError"?c("音頻權限被拒絕",s.AUDIO_PERMISSION_DENIED,t):t.name==="NotFoundError"?c("找不到音頻設備",s.AUDIO_DEVICE_NOT_FOUND,t):c(`獲取音頻流失敗: ${t.message}`,s.RECORDING_FAILED,t)}}setupMediaRecorderEvents(){this.mediaRecorder&&(this.mediaRecorder.ondataavailable=e=>{e.data.size>0&&this.audioChunks.push(e.data)},this.mediaRecorder.onerror=e=>{this.emit("recording-error",e.error)})}async createAudioBlob(){if(this.audioChunks.length===0)throw c("沒有音頻數據",s.RECORDING_FAILED);return new Blob(this.audioChunks,{type:"audio/webm"})}startSilenceDetection(){if(this.audioStream)try{const e=new AudioContext,t=e.createMediaStreamSource(this.audioStream),i=e.createAnalyser();i.fftSize=256,t.connect(i);const n=i.frequencyBinCount,o=new Uint8Array(n);let a=0,l=Date.now();const g=()=>{if(!this.isRecording)return;if(i.getByteFrequencyData(o),o.reduce((D,X)=>D+X,0)/n/255>y.silenceThreshold)l=Date.now(),a=0;else if(a===0&&(a=Date.now()),Date.now()-a>y.silenceTimeout){this.emit("silence-detected");return}this.silenceDetectionTimer=setTimeout(g,100)};g()}catch(e){console.warn("靜音檢測啟動失敗:",e)}}async cleanup(){this.isRecording=!1,this.silenceDetectionTimer&&(clearTimeout(this.silenceDetectionTimer),this.silenceDetectionTimer=null),this.audioStream&&(this.audioStream.getTracks().forEach(e=>e.stop()),this.audioStream=null),this.mediaRecorder=null,this.audioChunks=[]}}class de extends I.EventEmitter{constructor(){super(),this.speechConfig=null,this.recognizer=null}initialize(e){try{this.speechConfig=p.SpeechConfig.fromSubscription(e.apiKey,e.region),this.speechConfig.speechRecognitionLanguage=e.language,this.speechConfig.outputFormat=p.OutputFormat.Detailed,this.speechConfig.setProperty(p.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs,"5000"),this.speechConfig.setProperty(p.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs,"3000"),this.emit("initialized")}catch(t){throw c(`Azure Speech Service 初始化失敗: ${t.message}`,s.SPEECH_SERVICE_ERROR,t)}}async transcribe(e,t){this.speechConfig||this.initialize(t);try{return await N(async()=>await this.performTranscription(e),E.retryAttempts,E.retryDelay)}catch(i){throw c(`語音轉文字失敗: ${i.message}`,s.SPEECH_SERVICE_ERROR,i)}}async startContinuousRecognition(e,t,i,n){this.speechConfig||this.initialize(t);try{const o=this.createAudioConfigFromStream(e);this.recognizer=new p.SpeechRecognizer(this.speechConfig,o),this.setupRecognizerEvents(i,n),this.recognizer.startContinuousRecognitionAsync(()=>{this.emit("recognition-started")},a=>{n(c(`連續語音識別啟動失敗: ${a}`,s.SPEECH_SERVICE_ERROR))})}catch(o){throw c(`連續語音識別失敗: ${o.message}`,s.SPEECH_SERVICE_ERROR,o)}}async stopContinuousRecognition(){if(this.recognizer)return new Promise((e,t)=>{this.recognizer.stopContinuousRecognitionAsync(()=>{this.cleanup(),this.emit("recognition-stopped"),e()},i=>{this.cleanup(),t(c(`停止連續語音識別失敗: ${i}`,s.SPEECH_SERVICE_ERROR))})})}async testConnection(e){try{const t=p.SpeechConfig.fromSubscription(e.apiKey,e.region),i=p.AudioConfig.fromDefaultMicrophoneInput(),n=new p.SpeechRecognizer(t,i);return new Promise(o=>{n.recognizeOnceAsync(()=>{n.close(),o(!0)},()=>{n.close(),o(!1)}),setTimeout(()=>{n.close(),o(!1)},5e3)})}catch{return!1}}async performTranscription(e){if(!this.speechConfig)throw c("Speech Service 未初始化",s.SPEECH_SERVICE_ERROR);return new Promise(async(t,i)=>{try{const n=await B(e),o=p.AudioConfig.fromWavFileInput(new Uint8Array(n)),a=new p.SpeechRecognizer(this.speechConfig,o);a.recognizeOnceAsync(l=>{a.close(),l.reason===p.ResultReason.RecognizedSpeech?t({text:l.text,confidence:this.extractConfidence(l)}):l.reason===p.ResultReason.NoMatch?i(c("無法識別語音內容",s.SPEECH_SERVICE_ERROR)):i(c(`語音識別失敗: ${l.errorDetails}`,s.SPEECH_SERVICE_ERROR))},l=>{a.close(),i(c(`語音識別錯誤: ${l}`,s.SPEECH_SERVICE_ERROR))}),setTimeout(()=>{a.close(),i(c("語音識別超時",s.SPEECH_SERVICE_ERROR))},E.timeout)}catch(n){i(c(`語音轉文字處理失敗: ${n.message}`,s.SPEECH_SERVICE_ERROR,n))}})}createAudioConfigFromStream(e){return p.AudioConfig.fromDefaultMicrophoneInput()}setupRecognizerEvents(e,t){this.recognizer&&(this.recognizer.recognized=(i,n)=>{n.result.reason===p.ResultReason.RecognizedSpeech&&e({text:n.result.text,confidence:this.extractConfidence(n.result)})},this.recognizer.recognizing=(i,n)=>{this.emit("recognizing",n.result.text)},this.recognizer.canceled=(i,n)=>{n.reason===p.CancellationReason.Error&&t(c(`語音識別被取消: ${n.errorDetails}`,s.SPEECH_SERVICE_ERROR))})}extractConfidence(e){try{const t=JSON.parse(e.json);if(t.NBest&&t.NBest.length>0)return t.NBest[0].Confidence||.5}catch{}return .5}cleanup(){this.recognizer&&(this.recognizer.close(),this.recognizer=null)}destroy(){this.cleanup(),this.speechConfig=null,this.removeAllListeners()}}class pe extends I.EventEmitter{constructor(){super(),this.httpClient=null,this.config=null}initialize(e){try{this.config=e,this.httpClient=F.create({baseURL:e.endpoint,timeout:E.timeout,headers:{"api-key":e.apiKey,"Content-Type":"application/json"}}),this.httpClient.interceptors.request.use(t=>(this.emit("request-start",t),t),t=>(this.emit("request-error",t),Promise.reject(t))),this.httpClient.interceptors.response.use(t=>(this.emit("response-success",t),t),t=>(this.emit("response-error",t),Promise.reject(t))),this.emit("initialized")}catch(t){throw c(`Azure OpenAI Service 初始化失敗: ${t.message}`,s.OPENAI_SERVICE_ERROR,t)}}async processAudio(e,t){this.httpClient||this.initialize(t);try{return await N(async()=>await this.performAudioProcessing(e),E.retryAttempts,E.retryDelay)}catch(i){throw c(`AI 音頻處理失敗: ${i.message}`,s.OPENAI_SERVICE_ERROR,i)}}async processText(e,t,i){this.httpClient||this.initialize(t);try{return await N(async()=>await this.performTextProcessing(e,i),E.retryAttempts,E.retryDelay)}catch(n){throw c(`AI 文字處理失敗: ${n.message}`,s.OPENAI_SERVICE_ERROR,n)}}async testConnection(e){try{return this.httpClient||this.initialize(e),(await this.httpClient.get("/models")).status===200}catch{return!1}}async performAudioProcessing(e){if(!this.httpClient||!this.config)throw c("OpenAI Service 未初始化",s.OPENAI_SERVICE_ERROR);try{const t=await B(e),i=Buffer.from(t).toString("base64"),n={model:this.config.model,messages:[{role:"system",content:this.getSystemPrompt()},{role:"user",content:[{type:"text",text:"請理解這段語音內容，並根據指示提供適當的回應。如果是寫作任務，請直接提供內容；如果是問題，請提供答案；如果是指令，請執行相應操作。"},{type:"input_audio",input_audio:{data:i,format:"webm"}}]}],max_tokens:1e3,temperature:.7},o=await this.httpClient.post(`/openai/deployments/${this.config.model}/chat/completions?api-version=2024-10-01-preview`,n);return this.parseResponse(o.data)}catch(t){throw this.handleAPIError(t)}}async performTextProcessing(e,t){if(!this.httpClient||!this.config)throw c("OpenAI Service 未初始化",s.OPENAI_SERVICE_ERROR);try{const i={model:this.config.model,messages:[{role:"system",content:this.getSystemPrompt()},{role:"user",content:t?`上下文：${t}

用戶指令：${e}`:e}],max_tokens:1e3,temperature:.7},n=await this.httpClient.post(`/openai/deployments/${this.config.model}/chat/completions?api-version=2024-10-01-preview`,i);return this.parseResponse(n.data,e)}catch(i){throw this.handleAPIError(i)}}getSystemPrompt(){return`你是 SpeechPilot AI 助手，專門處理語音輸入指令。你的任務是：

1. 理解用戶的語音指令或文字指令
2. 根據指令類型提供適當的回應：
   - 寫作任務：直接提供所需內容（如文章、郵件、社交媒體貼文等）
   - 問題回答：提供準確、簡潔的答案
   - 翻譯任務：提供準確的翻譯
   - 格式化任務：按要求格式化內容
   - 其他指令：執行相應操作

3. 回應原則：
   - 直接提供可用的內容，不需要額外說明
   - 保持簡潔但完整
   - 使用繁體中文回應（除非特別要求其他語言）
   - 如果指令不清楚，提供最合理的解釋

4. 特殊處理：
   - 如果是創作任務，直接提供創作內容
   - 如果是技術問題，提供實用的解決方案
   - 如果是日常對話，提供友善的回應

請根據用戶的指令提供最適合直接輸入到文字欄位的內容。`}parseResponse(e,t){var i,n;try{const o=(i=e.choices)==null?void 0:i[0];if(!o)throw new Error("無效的 API 回應格式");const a=((n=o.message)==null?void 0:n.content)||"",l=this.detectIntent(t||a);return{originalText:t||"語音輸入",processedText:a.trim(),intent:l}}catch(o){throw c(`解析 AI 回應失敗: ${o.message}`,s.OPENAI_SERVICE_ERROR,o)}}detectIntent(e){const t=e.toLowerCase();return t.includes("寫")||t.includes("文章")||t.includes("內容")?"writing":t.includes("翻譯")||t.includes("translate")?"translation":t.includes("問")||t.includes("什麼")||t.includes("?")?"question":t.includes("格式")||t.includes("整理")?"formatting":"general"}handleAPIError(e){var t,i,n,o;if(F.isAxiosError(e)){const a=(t=e.response)==null?void 0:t.status,l=((o=(n=(i=e.response)==null?void 0:i.data)==null?void 0:n.error)==null?void 0:o.message)||e.message;switch(a){case 401:return c("API 金鑰無效",s.OPENAI_SERVICE_ERROR,e);case 403:return c("API 權限不足",s.OPENAI_SERVICE_ERROR,e);case 429:return c("API 請求頻率超限",s.OPENAI_SERVICE_ERROR,e);case 500:return c("OpenAI 服務器錯誤",s.OPENAI_SERVICE_ERROR,e);default:return c(`OpenAI API 錯誤: ${l}`,s.OPENAI_SERVICE_ERROR,e)}}return c(`OpenAI 服務錯誤: ${e.message}`,s.OPENAI_SERVICE_ERROR,e)}destroy(){this.httpClient=null,this.config=null,this.removeAllListeners()}}class fe extends I.EventEmitter{constructor(){super(),this.isTyping=!1,this.typingSpeed=50,this.useClipboard=!1,this.setupKeyboard()}setupKeyboard(){h.keyboard.config.autoDelayMs=this.typingSpeed}async typeText(e,t){if(this.isTyping)throw c("正在輸入文字中，請稍候",s.TEXT_INPUT_FAILED);if(!e||e.trim().length===0)throw c("輸入文字不能為空",s.TEXT_INPUT_FAILED);try{this.isTyping=!0,this.emit("typing-started",e);const i=(t==null?void 0:t.useClipboard)??this.useClipboard,n=(t==null?void 0:t.typingSpeed)??this.typingSpeed,o=(t==null?void 0:t.clearExisting)??!1;await w(100),o&&await this.clearCurrentText(),i?await this.typeViaClipboard(e):await this.typeDirectly(e,n),this.emit("typing-completed",e)}catch(i){throw this.emit("typing-error",i),c(`文字輸入失敗: ${i.message}`,s.TEXT_INPUT_FAILED,i)}finally{this.isTyping=!1}}async typeDirectly(e,t){try{const i=h.keyboard.config.autoDelayMs;h.keyboard.config.autoDelayMs=t;for(const n of e)if(this.isSpecialCharacter(n)?await this.typeSpecialCharacter(n):await h.keyboard.type(n),!this.isTyping)break;h.keyboard.config.autoDelayMs=i}catch(i){throw c(`直接輸入失敗: ${i.message}`,s.TEXT_INPUT_FAILED,i)}}async typeViaClipboard(e){try{let t="";try{t=await h.clipboard.readText()}catch{}if(await h.clipboard.writeText(e),await w(50),await h.keyboard.pressKey(h.Key.LeftControl,h.Key.V),await h.keyboard.releaseKey(h.Key.LeftControl,h.Key.V),await w(100),t)try{await h.clipboard.writeText(t)}catch{}}catch(t){throw c(`剪貼簿輸入失敗: ${t.message}`,s.TEXT_INPUT_FAILED,t)}}async clearCurrentText(){try{await h.keyboard.pressKey(h.Key.LeftControl,h.Key.A),await h.keyboard.releaseKey(h.Key.LeftControl,h.Key.A),await w(50),await h.keyboard.pressKey(h.Key.Delete),await h.keyboard.releaseKey(h.Key.Delete),await w(50)}catch(e){throw c(`清除文字失敗: ${e.message}`,s.TEXT_INPUT_FAILED,e)}}isSpecialCharacter(e){return[`
`,"	","\r"].includes(e)}async typeSpecialCharacter(e){switch(e){case`
`:case"\r":await h.keyboard.pressKey(h.Key.Return),await h.keyboard.releaseKey(h.Key.Return);break;case"	":await h.keyboard.pressKey(h.Key.Tab),await h.keyboard.releaseKey(h.Key.Tab);break;default:await h.keyboard.type(e)}}async pressKeyCombo(e){try{await h.keyboard.pressKey(...e),await h.keyboard.releaseKey(...e)}catch(t){throw c(`按鍵組合失敗: ${t.message}`,s.TEXT_INPUT_FAILED,t)}}async insertText(e){await this.typeText(e,{clearExisting:!1})}async replaceSelectedText(e){try{await this.typeText(e,{clearExisting:!1})}catch(t){throw c(`替換文字失敗: ${t.message}`,s.TEXT_INPUT_FAILED,t)}}setInputMode(e){this.useClipboard=e,this.emit("input-mode-changed",e)}setTypingSpeed(e){if(e<10||e>1e3)throw c("輸入速度必須在 10-1000 毫秒之間",s.TEXT_INPUT_FAILED);this.typingSpeed=e,h.keyboard.config.autoDelayMs=e,this.emit("typing-speed-changed",e)}stopTyping(){this.isTyping=!1,this.emit("typing-stopped")}getIsTyping(){return this.isTyping}getConfig(){return{typingSpeed:this.typingSpeed,useClipboard:this.useClipboard,isTyping:this.isTyping}}async testInput(){try{return await this.typeText("SpeechPilot Test",{useClipboard:!1,typingSpeed:10}),!0}catch{return!1}}destroy(){this.stopTyping(),this.removeAllListeners()}}class ge extends I.EventEmitter{constructor(){super(),this.errorLogs=[],this.maxLogEntries=1e3,this.showNotifications=!0,this.setupLogFile(),this.setupGlobalErrorHandlers()}setupLogFile(){const e=u.app.getPath("userData"),t=f.join(e,"logs");b.existsSync(t)||b.mkdirSync(t,{recursive:!0});const i=new Date().toISOString().split("T")[0];this.logFilePath=f.join(t,`speechpilot-${i}.log`)}setupGlobalErrorHandlers(){process.on("uncaughtException",e=>{this.handleError(e,"error","UNCAUGHT_EXCEPTION")}),process.on("unhandledRejection",(e,t)=>{this.handleError(new Error(`Unhandled Promise Rejection: ${e}`),"error","UNHANDLED_REJECTION",{promise:t,reason:e})})}handleError(e,t="error",i,n){const o=i||e.code||"UNKNOWN_ERROR",a=e.message||"未知錯誤",l={timestamp:new Date().toISOString(),level:t,code:o,message:a,details:n,stack:e.stack};this.errorLogs.push(l),this.errorLogs.length>this.maxLogEntries&&this.errorLogs.shift(),this.writeToLogFile(l),this.showNotifications&&t==="error"&&this.showErrorNotification(a,o),this.emit("error-logged",l),t==="error"&&this.shouldShowDialog(o)&&this.showErrorDialog(a,o,n),console.error(`[${t.toUpperCase()}] ${o}: ${a}`,n)}writeToLogFile(e){try{const t=`${e.timestamp} [${e.level.toUpperCase()}] ${e.code}: ${e.message}
`;b.writeFileSync(this.logFilePath,t,{flag:"a"})}catch(t){console.error("寫入日誌檔案失敗:",t)}}showErrorNotification(e,t){try{const i=new u.Notification({title:"SpeechPilot 錯誤",body:this.getUserFriendlyMessage(e,t),icon:this.getNotificationIcon(),urgency:"critical"});i.show(),i.on("click",()=>{this.showMainWindow()})}catch(i){console.error("顯示通知失敗:",i)}}async showErrorDialog(e,t,i){try{const n=u.BrowserWindow.getFocusedWindow()||u.BrowserWindow.getAllWindows()[0],o={type:"error",title:"SpeechPilot 錯誤",message:this.getUserFriendlyMessage(e,t),detail:this.getErrorSolution(t),buttons:["確定","查看詳細資訊","回報問題"],defaultId:0,cancelId:0},a=await u.dialog.showMessageBox(n,o);a.response===1?this.showDetailedError(e,t,i):a.response===2&&this.reportIssue(e,t,i)}catch(n){console.error("顯示錯誤對話框失敗:",n)}}async showDetailedError(e,t,i){const n=`
錯誤代碼: ${t}
錯誤訊息: ${e}
時間: ${new Date().toLocaleString()}
詳細資訊: ${JSON.stringify(i,null,2)}
    `.trim();await u.dialog.showMessageBox(u.BrowserWindow.getFocusedWindow()||u.BrowserWindow.getAllWindows()[0],{type:"info",title:"錯誤詳細資訊",message:"錯誤詳細資訊",detail:n,buttons:["確定","複製到剪貼簿"],defaultId:0})}getUserFriendlyMessage(e,t){return{[s.AUDIO_PERMISSION_DENIED]:"無法存取麥克風，請檢查權限設定",[s.AUDIO_DEVICE_NOT_FOUND]:"找不到音頻設備，請檢查麥克風連接",[s.RECORDING_FAILED]:"錄音失敗，請重試",[s.SPEECH_SERVICE_ERROR]:"Azure 語音服務錯誤，請檢查網路連接和 API 設定",[s.OPENAI_SERVICE_ERROR]:"Azure OpenAI 服務錯誤，請檢查 API 設定",[s.TEXT_INPUT_FAILED]:"文字輸入失敗，請確保目標應用程式處於焦點狀態",[s.CONFIG_LOAD_FAILED]:"載入配置失敗，將使用預設設定",[s.CONFIG_SAVE_FAILED]:"儲存配置失敗，請檢查檔案權限",[s.HOTKEY_REGISTRATION_FAILED]:"快捷鍵註冊失敗，可能與其他應用程式衝突"}[t]||e}getErrorSolution(e){return{[s.AUDIO_PERMISSION_DENIED]:"請在系統設定中允許 SpeechPilot 存取麥克風",[s.AUDIO_DEVICE_NOT_FOUND]:"請檢查麥克風是否正確連接並在系統中可見",[s.RECORDING_FAILED]:"請重新啟動應用程式或檢查音頻設備",[s.SPEECH_SERVICE_ERROR]:"請檢查網路連接和 Azure Speech Service API 金鑰",[s.OPENAI_SERVICE_ERROR]:"請檢查 Azure OpenAI API 金鑰和端點設定",[s.TEXT_INPUT_FAILED]:"請確保目標應用程式的文字欄位處於焦點狀態",[s.CONFIG_LOAD_FAILED]:"請檢查配置檔案是否存在且格式正確",[s.CONFIG_SAVE_FAILED]:"請檢查應用程式是否有寫入權限",[s.HOTKEY_REGISTRATION_FAILED]:"請嘗試更改快捷鍵組合或關閉衝突的應用程式"}[e]||"請聯繫技術支援獲取協助"}shouldShowDialog(e){return[s.AUDIO_PERMISSION_DENIED,s.CONFIG_LOAD_FAILED,s.HOTKEY_REGISTRATION_FAILED].includes(e)}getNotificationIcon(){return f.join(__dirname,"../../../assets/tray-icon.png")}showMainWindow(){const e=u.BrowserWindow.getAllWindows().find(t=>!t.isDestroyed());e&&(e.show(),e.focus())}reportIssue(e,t,i){console.log("回報問題:",{message:e,code:t,details:i}),this.emit("issue-reported",{message:e,code:t,details:i})}logInfo(e,t){this.handleError(new Error(e),"info","INFO",t)}logWarning(e,t){this.handleError(new Error(e),"warning","WARNING",t)}getErrorLogs(e){return e?this.errorLogs.filter(t=>t.level===e):[...this.errorLogs]}clearLogs(){this.errorLogs=[],this.emit("logs-cleared")}setNotificationsEnabled(e){this.showNotifications=e}exportLogs(){return JSON.stringify(this.errorLogs,null,2)}destroy(){this.removeAllListeners()}}class Ee{constructor(){this.mainWindow=null,this.recordingWindow=null,this.tray=null,this.currentSession=null,this.isRecording=!1,this.configManager=new ue,this.audioRecorder=new le,this.speechService=new de,this.openaiService=new pe,this.textInputService=new fe,this.errorHandler=new ge,this.setupEventListeners()}async initialize(){await this.configManager.initialize(),await this.createMainWindow(),await this.createTray(),await this.registerGlobalShortcuts(),this.setupIPC()}async createMainWindow(){this.mainWindow=new u.BrowserWindow({...C.main,icon:this.getAppIcon(),webPreferences:{...C.main.webPreferences,preload:f.join(__dirname,"../preload/preload.js")}}),process.env.NODE_ENV==="development"?(this.mainWindow.loadURL("http://localhost:3000"),this.mainWindow.webContents.openDevTools()):this.mainWindow.loadFile(f.join(__dirname,"../renderer/index.html")),this.mainWindow.on("close",e=>{var t;this.configManager.getConfig().minimizeToTray&&(e.preventDefault(),(t=this.mainWindow)==null||t.hide())}),this.mainWindow.on("closed",()=>{this.mainWindow=null})}async createRecordingWindow(){this.recordingWindow||(this.recordingWindow=new u.BrowserWindow({...C.recording,icon:this.getAppIcon(),webPreferences:{...C.recording.webPreferences,preload:f.join(__dirname,"../preload/preload.js")}}),process.env.NODE_ENV==="development"?this.recordingWindow.loadURL("http://localhost:3000/recording"):this.recordingWindow.loadFile(f.join(__dirname,"../renderer/recording.html")),this.recordingWindow.on("closed",()=>{this.recordingWindow=null}))}createTray(){const e=this.getAppIcon();this.tray=new u.Tray(e);const t=u.Menu.buildFromTemplate([{label:"顯示主視窗",click:()=>this.showMainWindow()},{label:"開始 AI 語音輸入",click:()=>this.startRecording(m.AI_SPEECH_TO_TEXT)},{label:"開始直接語音輸入",click:()=>this.startRecording(m.DIRECT_SPEECH_TO_TEXT)},{type:"separator"},{label:"設定",click:()=>this.showMainWindow()},{label:"退出",click:()=>u.app.quit()}]);this.tray.setContextMenu(t),this.tray.setToolTip(se.name),this.tray.on("double-click",()=>{this.showMainWindow()})}getAppIcon(){const e=f.join(__dirname,"../../assets/tray-icon.png");return u.nativeImage.createFromPath(e)}showMainWindow(){this.mainWindow&&(this.mainWindow.show(),this.mainWindow.focus())}async registerGlobalShortcuts(){const e=this.configManager.getConfig();u.globalShortcut.register(e.hotkeys.aiSpeechToText,()=>{this.handleHotkeyPress(m.AI_SPEECH_TO_TEXT)}),u.globalShortcut.register(e.hotkeys.directSpeechToText,()=>{this.handleHotkeyPress(m.DIRECT_SPEECH_TO_TEXT)})}handleHotkeyPress(e){this.isRecording?this.stopRecording():this.startRecording(e)}async startRecording(e){var t;if(!this.isRecording)try{this.isRecording=!0,this.currentSession={id:ae(),mode:e,state:_.RECORDING,startTime:Date.now()},await this.createRecordingWindow(),(t=this.recordingWindow)==null||t.show(),await this.audioRecorder.startRecording(),this.broadcastSessionUpdate()}catch(i){this.handleError(i)}}async stopRecording(){if(!(!this.isRecording||!this.currentSession))try{this.currentSession.state=_.PROCESSING,this.broadcastSessionUpdate();const e=await this.audioRecorder.stopRecording();this.currentSession.audioData=e,this.currentSession.endTime=Date.now(),await this.processAudio()}catch(e){this.handleError(e)}}async processAudio(){var e;if((e=this.currentSession)!=null&&e.audioData)try{const t=this.configManager.getConfig();if(this.currentSession.mode===m.DIRECT_SPEECH_TO_TEXT){const i=await this.speechService.transcribe(this.currentSession.audioData,t.azure.speech);this.currentSession.transcription=i.text,await this.textInputService.typeText(i.text)}else{const i=await this.openaiService.processAudio(this.currentSession.audioData,t.azure.openai);this.currentSession.transcription=i.originalText,this.currentSession.aiResponse=i.processedText,await this.textInputService.typeText(i.processedText)}this.currentSession.state=_.COMPLETED,this.broadcastSessionUpdate(),setTimeout(()=>{var i;(i=this.recordingWindow)==null||i.close(),this.isRecording=!1,this.currentSession=null},2e3)}catch(t){this.handleError(t)}}setupEventListeners(){this.configManager.on("config-changed",e=>{var t;(t=this.mainWindow)==null||t.webContents.send("config-updated",e),this.updateGlobalShortcuts(e)})}updateGlobalShortcuts(e){u.globalShortcut.unregisterAll(),this.registerGlobalShortcuts()}setupIPC(){u.ipcMain.handle("get-config",()=>this.configManager.getConfig()),u.ipcMain.handle("update-config",async(e,t)=>{await this.configManager.updateConfig(t)}),u.ipcMain.handle("start-recording",(e,{mode:t})=>{this.startRecording(t)}),u.ipcMain.handle("stop-recording",()=>{this.stopRecording()}),u.ipcMain.handle("get-audio-devices",async()=>await this.audioRecorder.getAudioDevices()),u.ipcMain.handle("minimize-to-tray",()=>{var e;(e=this.mainWindow)==null||e.hide()}),u.ipcMain.handle("show-window",()=>{this.showMainWindow()})}broadcastSessionUpdate(){var e,t;this.currentSession&&((e=this.mainWindow)==null||e.webContents.send("recording-state-changed",this.currentSession),(t=this.recordingWindow)==null||t.webContents.send("recording-state-changed",this.currentSession))}handleError(e){var t;this.currentSession.state=_.ERROR,this.currentSession.error=e.message,this.broadcastSessionUpdate(),this.errorHandler.handleError(e),(t=this.mainWindow)==null||t.webContents.send("error-occurred",{message:e.message,details:e}),this.isRecording=!1,setTimeout(()=>{var i;(i=this.recordingWindow)==null||i.close(),this.currentSession=null},3e3)}cleanup(){var e;u.globalShortcut.unregisterAll(),(e=this.tray)==null||e.destroy()}}const P=new Ee;u.app.whenReady().then(async()=>{await P.initialize()});u.app.on("window-all-closed",()=>{process.platform!=="darwin"&&u.app.quit()});u.app.on("activate",()=>{u.BrowserWindow.getAllWindows().length===0&&P.initialize()});u.app.on("before-quit",()=>{P.cleanup()});
