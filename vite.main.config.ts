import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  mode: process.env.NODE_ENV || 'production',
  root: resolve(__dirname, 'src/main'),
  build: {
    outDir: resolve(__dirname, 'dist/main'),
    lib: {
      entry: 'main.ts',
      formats: ['cjs'],
      fileName: () => 'main.js'
    },
    rollupOptions: {
      external: [
        'electron',
        '@nut-tree-fork/nut-js',
        'microsoft-cognitiveservices-speech-sdk',
        'axios',
        'dotenv',
        'electron-store',
        'path',
        'fs',
        'os'
      ]
    },
    minify: process.env.NODE_ENV === 'production',
    sourcemap: process.env.NODE_ENV === 'development'
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@main': resolve(__dirname, 'src/main'),
      '@shared': resolve(__dirname, 'src/shared')
    }
  }
});
