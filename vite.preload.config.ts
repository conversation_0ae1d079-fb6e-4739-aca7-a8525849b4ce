import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  mode: process.env.NODE_ENV || 'production',
  root: resolve(__dirname, 'src/preload'),
  build: {
    outDir: resolve(__dirname, 'dist/preload'),
    lib: {
      entry: 'preload.ts',
      formats: ['cjs'],
      fileName: () => 'preload.js'
    },
    rollupOptions: {
      external: [
        'electron'
      ]
    },
    minify: process.env.NODE_ENV === 'production',
    sourcemap: process.env.NODE_ENV === 'development'
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@shared': resolve(__dirname, 'src/shared')
    }
  }
});
