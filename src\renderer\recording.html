<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SpeechPilot - 錄音中</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      user-select: none;
      -webkit-app-region: drag;
    }
    
    .container {
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 2rem;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      min-width: 250px;
    }
    
    .recording-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      animation: pulse 1.5s ease-in-out infinite;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.1); opacity: 0.7; }
    }
    
    .status {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    .mode {
      font-size: 0.9rem;
      opacity: 0.8;
      margin-bottom: 1rem;
    }
    
    .timer {
      font-size: 1.5rem;
      font-weight: 700;
      color: #ff6b6b;
      margin-bottom: 1rem;
    }
    
    .stop-btn {
      background: #ff6b6b;
      border: none;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      color: white;
      font-size: 1.5rem;
      cursor: pointer;
      transition: all 0.3s ease;
      -webkit-app-region: no-drag;
    }
    
    .stop-btn:hover {
      background: #ff5252;
      transform: scale(1.1);
    }
    
    .stop-btn:active {
      transform: scale(0.95);
    }
    
    .processing {
      display: none;
    }
    
    .processing.active {
      display: block;
    }
    
    .spinner {
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 1rem auto;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .hint {
      font-size: 0.8rem;
      opacity: 0.6;
      margin-top: 1rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div id="recording-view">
      <div class="recording-icon">🎤</div>
      <div class="status" id="status">正在錄音</div>
      <div class="mode" id="mode">AI 語音輸入</div>
      <div class="timer" id="timer">00:00</div>
      <button class="stop-btn" id="stop-btn" title="停止錄音">⏹</button>
      <div class="hint">再次按下快捷鍵或點擊停止按鈕結束錄音</div>
    </div>
    
    <div id="processing-view" class="processing">
      <div class="spinner"></div>
      <div class="status">正在處理</div>
      <div class="mode" id="processing-mode">請稍候...</div>
    </div>
    
    <div id="completed-view" class="processing">
      <div class="recording-icon">✅</div>
      <div class="status">完成</div>
      <div class="mode" id="result-text">文字已輸入</div>
    </div>
    
    <div id="error-view" class="processing">
      <div class="recording-icon">❌</div>
      <div class="status">錯誤</div>
      <div class="mode" id="error-text">發生錯誤</div>
    </div>
  </div>

  <script>
    let startTime = Date.now();
    let timerInterval = null;
    let currentSession = null;
    
    // DOM 元素
    const recordingView = document.getElementById('recording-view');
    const processingView = document.getElementById('processing-view');
    const completedView = document.getElementById('completed-view');
    const errorView = document.getElementById('error-view');
    const statusEl = document.getElementById('status');
    const modeEl = document.getElementById('mode');
    const timerEl = document.getElementById('timer');
    const stopBtn = document.getElementById('stop-btn');
    const processingMode = document.getElementById('processing-mode');
    const resultText = document.getElementById('result-text');
    const errorText = document.getElementById('error-text');
    
    // 檢查 Electron API 是否可用
    if (window.electronAPI) {
      initializeRecording();
    } else {
      showError('無法連接到主程序');
    }
    
    function initializeRecording() {
      // 開始計時器
      startTimer();
      
      // 設置事件監聽器
      setupEventListeners();
    }
    
    function setupEventListeners() {
      // 停止按鈕
      stopBtn.addEventListener('click', () => {
        window.electronAPI.stopRecording();
      });
      
      // 監聽錄音狀態變化
      window.electronAPI.onRecordingStateChanged((session) => {
        currentSession = session;
        updateView(session);
      });
      
      // 監聽錯誤
      window.electronAPI.onErrorOccurred((error) => {
        showError(error.message);
      });
    }
    
    function startTimer() {
      startTime = Date.now();
      timerInterval = setInterval(updateTimer, 100);
    }
    
    function updateTimer() {
      const elapsed = Date.now() - startTime;
      const seconds = Math.floor(elapsed / 1000);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      
      timerEl.textContent = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    function stopTimer() {
      if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
      }
    }
    
    function updateView(session) {
      // 隱藏所有視圖
      hideAllViews();
      
      // 更新模式顯示
      const modeText = session.mode === 'ai_speech_to_text' ? 'AI 語音輸入' : '直接語音輸入';
      modeEl.textContent = modeText;
      processingMode.textContent = `正在處理 ${modeText}...`;
      
      switch (session.state) {
        case 'recording':
          recordingView.style.display = 'block';
          break;
          
        case 'processing':
          stopTimer();
          processingView.style.display = 'block';
          break;
          
        case 'completed':
          completedView.style.display = 'block';
          if (session.aiResponse) {
            resultText.textContent = `已輸入：${session.aiResponse.substring(0, 50)}${session.aiResponse.length > 50 ? '...' : ''}`;
          } else if (session.transcription) {
            resultText.textContent = `已輸入：${session.transcription.substring(0, 50)}${session.transcription.length > 50 ? '...' : ''}`;
          } else {
            resultText.textContent = '文字已輸入';
          }
          break;
          
        case 'error':
          showError(session.error || '未知錯誤');
          break;
      }
    }
    
    function hideAllViews() {
      recordingView.style.display = 'none';
      processingView.style.display = 'none';
      completedView.style.display = 'none';
      errorView.style.display = 'none';
    }
    
    function showError(message) {
      stopTimer();
      hideAllViews();
      errorView.style.display = 'block';
      errorText.textContent = message;
    }
  </script>
</body>
</html>
