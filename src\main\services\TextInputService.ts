import { keyboard, clipboard, Key } from '@nut-tree-fork/nut-js';
import { EventEmitter } from 'events';
import { ERROR_CODES } from '../../shared/constants';
import { createError, delay } from '../../shared/utils';

export class TextInputService extends EventEmitter {
  private isTyping = false;
  private typingSpeed = 50; // 毫秒間隔
  private useClipboard = false; // 是否使用剪貼簿模式

  constructor() {
    super();
    this.setupKeyboard();
  }

  /**
   * 設置鍵盤配置
   */
  private setupKeyboard(): void {
    // 設置鍵盤延遲，避免輸入過快
    keyboard.config.autoDelayMs = this.typingSpeed;
  }

  /**
   * 輸入文字到當前焦點的文字欄位
   */
  async typeText(text: string, options?: {
    useClipboard?: boolean;
    typingSpeed?: number;
    clearExisting?: boolean;
  }): Promise<void> {
    if (this.isTyping) {
      throw createError('正在輸入文字中，請稍候', ERROR_CODES.TEXT_INPUT_FAILED);
    }

    if (!text || text.trim().length === 0) {
      throw createError('輸入文字不能為空', ERROR_CODES.TEXT_INPUT_FAILED);
    }

    try {
      this.isTyping = true;
      this.emit('typing-started', text);

      const useClipboard = options?.useClipboard ?? this.useClipboard;
      const typingSpeed = options?.typingSpeed ?? this.typingSpeed;
      const clearExisting = options?.clearExisting ?? false;

      // 短暫延遲確保焦點正確
      await delay(100);

      // 清除現有內容（如果需要）
      if (clearExisting) {
        await this.clearCurrentText();
      }

      if (useClipboard) {
        await this.typeViaClipboard(text);
      } else {
        await this.typeDirectly(text, typingSpeed);
      }

      this.emit('typing-completed', text);
    } catch (error) {
      this.emit('typing-error', error);
      throw createError(
        `文字輸入失敗: ${(error as Error).message}`,
        ERROR_CODES.TEXT_INPUT_FAILED,
        error
      );
    } finally {
      this.isTyping = false;
    }
  }

  /**
   * 直接鍵盤輸入
   */
  private async typeDirectly(text: string, speed: number): Promise<void> {
    try {
      // 設置輸入速度
      const originalDelay = keyboard.config.autoDelayMs;
      keyboard.config.autoDelayMs = speed;

      // 逐字符輸入
      for (const char of text) {
        if (this.isSpecialCharacter(char)) {
          await this.typeSpecialCharacter(char);
        } else {
          await keyboard.type(char);
        }
        
        // 檢查是否需要中斷
        if (!this.isTyping) break;
      }

      // 恢復原始延遲設置
      keyboard.config.autoDelayMs = originalDelay;
    } catch (error) {
      throw createError(
        `直接輸入失敗: ${(error as Error).message}`,
        ERROR_CODES.TEXT_INPUT_FAILED,
        error
      );
    }
  }

  /**
   * 通過剪貼簿輸入（適用於大量文字或特殊字符）
   */
  private async typeViaClipboard(text: string): Promise<void> {
    try {
      // 備份當前剪貼簿內容
      let originalClipboard = '';
      try {
        originalClipboard = await clipboard.readText();
      } catch {
        // 忽略讀取剪貼簿失敗的錯誤
      }

      // 將文字複製到剪貼簿
      await clipboard.writeText(text);
      
      // 短暫延遲確保剪貼簿更新
      await delay(50);

      // 使用 Ctrl+V 貼上
      await keyboard.pressKey(Key.LeftControl, Key.V);
      await keyboard.releaseKey(Key.LeftControl, Key.V);

      // 延遲後恢復原始剪貼簿內容
      await delay(100);
      if (originalClipboard) {
        try {
          await clipboard.writeText(originalClipboard);
        } catch {
          // 忽略恢復剪貼簿失敗的錯誤
        }
      }
    } catch (error) {
      throw createError(
        `剪貼簿輸入失敗: ${(error as Error).message}`,
        ERROR_CODES.TEXT_INPUT_FAILED,
        error
      );
    }
  }

  /**
   * 清除當前文字欄位的內容
   */
  private async clearCurrentText(): Promise<void> {
    try {
      // 使用 Ctrl+A 全選
      await keyboard.pressKey(Key.LeftControl, Key.A);
      await keyboard.releaseKey(Key.LeftControl, Key.A);
      
      await delay(50);
      
      // 按 Delete 鍵刪除
      await keyboard.pressKey(Key.Delete);
      await keyboard.releaseKey(Key.Delete);
      
      await delay(50);
    } catch (error) {
      throw createError(
        `清除文字失敗: ${(error as Error).message}`,
        ERROR_CODES.TEXT_INPUT_FAILED,
        error
      );
    }
  }

  /**
   * 檢查是否為特殊字符
   */
  private isSpecialCharacter(char: string): boolean {
    // 檢查是否為需要特殊處理的字符
    const specialChars = ['\n', '\t', '\r'];
    return specialChars.includes(char);
  }

  /**
   * 輸入特殊字符
   */
  private async typeSpecialCharacter(char: string): Promise<void> {
    switch (char) {
      case '\n':
      case '\r':
        await keyboard.pressKey(Key.Return);
        await keyboard.releaseKey(Key.Return);
        break;
      case '\t':
        await keyboard.pressKey(Key.Tab);
        await keyboard.releaseKey(Key.Tab);
        break;
      default:
        await keyboard.type(char);
    }
  }

  /**
   * 模擬按鍵組合
   */
  async pressKeyCombo(keys: Key[]): Promise<void> {
    try {
      await keyboard.pressKey(...keys);
      await keyboard.releaseKey(...keys);
    } catch (error) {
      throw createError(
        `按鍵組合失敗: ${(error as Error).message}`,
        ERROR_CODES.TEXT_INPUT_FAILED,
        error
      );
    }
  }

  /**
   * 插入文字到游標位置
   */
  async insertText(text: string): Promise<void> {
    await this.typeText(text, { clearExisting: false });
  }

  /**
   * 替換選中的文字
   */
  async replaceSelectedText(text: string): Promise<void> {
    try {
      // 直接輸入會替換選中的文字
      await this.typeText(text, { clearExisting: false });
    } catch (error) {
      throw createError(
        `替換文字失敗: ${(error as Error).message}`,
        ERROR_CODES.TEXT_INPUT_FAILED,
        error
      );
    }
  }

  /**
   * 設置輸入模式
   */
  setInputMode(useClipboard: boolean): void {
    this.useClipboard = useClipboard;
    this.emit('input-mode-changed', useClipboard);
  }

  /**
   * 設置輸入速度
   */
  setTypingSpeed(speed: number): void {
    if (speed < 10 || speed > 1000) {
      throw createError('輸入速度必須在 10-1000 毫秒之間', ERROR_CODES.TEXT_INPUT_FAILED);
    }
    
    this.typingSpeed = speed;
    keyboard.config.autoDelayMs = speed;
    this.emit('typing-speed-changed', speed);
  }

  /**
   * 停止當前輸入
   */
  stopTyping(): void {
    this.isTyping = false;
    this.emit('typing-stopped');
  }

  /**
   * 檢查是否正在輸入
   */
  getIsTyping(): boolean {
    return this.isTyping;
  }

  /**
   * 獲取當前配置
   */
  getConfig(): {
    typingSpeed: number;
    useClipboard: boolean;
    isTyping: boolean;
  } {
    return {
      typingSpeed: this.typingSpeed,
      useClipboard: this.useClipboard,
      isTyping: this.isTyping
    };
  }

  /**
   * 測試文字輸入功能
   */
  async testInput(): Promise<boolean> {
    try {
      // 測試簡單的文字輸入
      await this.typeText('SpeechPilot Test', { 
        useClipboard: false,
        typingSpeed: 10 
      });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 銷毀服務
   */
  destroy(): void {
    this.stopTyping();
    this.removeAllListeners();
  }
}
