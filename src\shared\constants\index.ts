// 預設配置
export const DEFAULT_CONFIG = {
  hotkeys: {
    aiSpeechToText: 'CommandOrControl+Shift+C',
    directSpeechToText: 'CommandOrControl+Shift+V',
    mode: 'toggle' as const
  },
  azure: {
    openai: {
      endpoint: '',
      apiKey: '',
      model: 'gpt-4o-mini-audio-preview'
    },
    speech: {
      region: 'eastus',
      apiKey: '',
      language: 'zh-TW'
    }
  },
  audioDevice: {
    deviceId: 'default',
    label: 'Default Microphone'
  },
  autoStart: false,
  minimizeToTray: true
};

// 應用程式資訊
export const APP_INFO = {
  name: 'SpeechPilot',
  version: '1.0.0',
  description: 'AI語音助手',
  author: 'SpeechPilot Team'
};

// 視窗配置
export const WINDOW_CONFIG = {
  main: {
    width: 400,
    height: 600,
    minWidth: 350,
    minHeight: 500,
    resizable: true,
    show: false,
    frame: true,
    titleBarStyle: 'default' as const,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    }
  },
  recording: {
    width: 300,
    height: 200,
    resizable: false,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    }
  }
};

// 音頻配置
export const AUDIO_CONFIG = {
  sampleRate: 16000,
  channels: 1,
  bitsPerSample: 16,
  maxRecordingTime: 60000, // 60 seconds
  silenceThreshold: 0.01,
  silenceTimeout: 3000 // 3 seconds
};

// API 配置
export const API_CONFIG = {
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000 // 1 second
};

// 錯誤代碼
export const ERROR_CODES = {
  AUDIO_PERMISSION_DENIED: 'AUDIO_PERMISSION_DENIED',
  AUDIO_DEVICE_NOT_FOUND: 'AUDIO_DEVICE_NOT_FOUND',
  RECORDING_FAILED: 'RECORDING_FAILED',
  SPEECH_SERVICE_ERROR: 'SPEECH_SERVICE_ERROR',
  OPENAI_SERVICE_ERROR: 'OPENAI_SERVICE_ERROR',
  TEXT_INPUT_FAILED: 'TEXT_INPUT_FAILED',
  CONFIG_LOAD_FAILED: 'CONFIG_LOAD_FAILED',
  CONFIG_SAVE_FAILED: 'CONFIG_SAVE_FAILED',
  HOTKEY_REGISTRATION_FAILED: 'HOTKEY_REGISTRATION_FAILED'
} as const;
