{"name": "speechpilot", "version": "1.0.0", "description": "AI語音助手 - 在任何應用程式中使用語音輸入和AI指令處理", "main": "dist/main/main.js", "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:preload\" \"npm run dev:renderer\"", "dev:main": "vite build --config vite.main.config.ts --mode development --watch", "dev:preload": "vite build --config vite.preload.config.ts --mode development --watch", "dev:renderer": "vite --config vite.renderer.config.ts", "build": "npm run build:main && npm run build:preload && npm run build:renderer", "build:main": "vite build --config vite.main.config.ts", "build:preload": "vite build --config vite.preload.config.ts", "build:renderer": "vite build --config vite.renderer.config.ts", "electron": "electron .", "electron:dev": "cross-env NODE_ENV=development electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "keywords": ["electron", "speech-to-text", "ai", "voice-input", "azure", "openai"], "author": "SpeechPilot Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "rimraf": "^5.0.5", "typescript": "^5.3.3", "vite": "^5.0.10", "vite-plugin-electron": "^0.28.1"}, "dependencies": {"@nut-tree-fork/nut-js": "^4.2.0", "axios": "^1.6.2", "dotenv": "^16.3.1", "electron-store": "^8.1.0", "framer-motion": "^10.16.16", "lucide-react": "^0.303.0", "microsoft-cognitiveservices-speech-sdk": "^1.34.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "build": {"appId": "com.speechpilot.app", "productName": "SpeechPilot", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "mac": {"category": "public.app-category.productivity", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}