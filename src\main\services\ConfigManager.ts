import Store from 'electron-store';
import { EventEmitter } from 'events';
import { AppConfig, HotkeyConfig, AzureConfig, AudioDeviceConfig } from '../../shared/types';
import { DEFAULT_CONFIG, ERROR_CODES } from '../../shared/constants';
import { createError, validateConfig, isValidHotkey } from '../../shared/utils';
import * as dotenv from 'dotenv';
import { join } from 'path';

export class ConfigManager extends EventEmitter {
  private store: Store<AppConfig>;
  private config: AppConfig;
  private isInitialized = false;

  constructor() {
    super();
    
    // 初始化 electron-store
    this.store = new Store<AppConfig>({
      name: 'speechpilot-config',
      defaults: DEFAULT_CONFIG,
      schema: {
        hotkeys: {
          type: 'object',
          properties: {
            aiSpeechToText: { type: 'string' },
            directSpeechToText: { type: 'string' },
            mode: { type: 'string', enum: ['press', 'toggle'] }
          }
        },
        azure: {
          type: 'object',
          properties: {
            openai: {
              type: 'object',
              properties: {
                endpoint: { type: 'string' },
                apiKey: { type: 'string' },
                model: { type: 'string' }
              }
            },
            speech: {
              type: 'object',
              properties: {
                region: { type: 'string' },
                apiKey: { type: 'string' },
                language: { type: 'string' }
              }
            }
          }
        },
        audioDevice: {
          type: 'object',
          properties: {
            deviceId: { type: 'string' },
            label: { type: 'string' }
          }
        },
        autoStart: { type: 'boolean' },
        minimizeToTray: { type: 'boolean' }
      }
    });

    this.config = this.store.store;
  }

  /**
   * 初始化配置管理器
   */
  async initialize(): Promise<void> {
    try {
      // 載入環境變數
      await this.loadEnvironmentVariables();
      
      // 載入儲存的配置
      this.loadStoredConfig();
      
      // 驗證配置
      await this.validateCurrentConfig();
      
      this.isInitialized = true;
      this.emit('initialized', this.config);
    } catch (error) {
      throw createError(
        `配置初始化失敗: ${(error as Error).message}`,
        ERROR_CODES.CONFIG_LOAD_FAILED,
        error
      );
    }
  }

  /**
   * 載入環境變數
   */
  private async loadEnvironmentVariables(): Promise<void> {
    try {
      // 載入 .env 檔案
      const envPath = join(process.cwd(), '.env');
      dotenv.config({ path: envPath });

      // 如果環境變數中有配置，則覆蓋預設值
      const envConfig: Partial<AppConfig> = {};

      if (process.env.AZURE_OPENAI_ENDPOINT) {
        envConfig.azure = {
          ...this.config.azure,
          openai: {
            ...this.config.azure.openai,
            endpoint: process.env.AZURE_OPENAI_ENDPOINT,
            apiKey: process.env.AZURE_OPENAI_API_KEY || this.config.azure.openai.apiKey,
            model: process.env.AZURE_OPENAI_MODEL || this.config.azure.openai.model
          }
        };
      }

      if (process.env.AZURE_SPEECH_SERVICE_REGION) {
        envConfig.azure = {
          ...envConfig.azure || this.config.azure,
          speech: {
            ...this.config.azure.speech,
            region: process.env.AZURE_SPEECH_SERVICE_REGION,
            apiKey: process.env.AZURE_SPEECH_SERVICE_API_KEY || this.config.azure.speech.apiKey,
            language: process.env.AZURE_SPEECH_LANGUAGE || this.config.azure.speech.language
          }
        };
      }

      // 合併環境變數配置
      if (Object.keys(envConfig).length > 0) {
        this.config = { ...this.config, ...envConfig };
      }
    } catch (error) {
      console.warn('載入環境變數失敗:', error);
    }
  }

  /**
   * 載入儲存的配置
   */
  private loadStoredConfig(): void {
    try {
      const storedConfig = this.store.store;
      this.config = { ...DEFAULT_CONFIG, ...storedConfig };
    } catch (error) {
      console.warn('載入儲存配置失敗，使用預設配置:', error);
      this.config = { ...DEFAULT_CONFIG };
    }
  }

  /**
   * 驗證當前配置
   */
  private async validateCurrentConfig(): Promise<void> {
    if (!validateConfig(this.config)) {
      console.warn('配置驗證失敗，某些功能可能無法正常使用');
    }

    // 驗證快捷鍵
    if (!isValidHotkey(this.config.hotkeys.aiSpeechToText)) {
      console.warn('AI 語音輸入快捷鍵格式無效');
    }

    if (!isValidHotkey(this.config.hotkeys.directSpeechToText)) {
      console.warn('直接語音輸入快捷鍵格式無效');
    }
  }

  /**
   * 獲取完整配置
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  async updateConfig(updates: Partial<AppConfig>): Promise<void> {
    if (!this.isInitialized) {
      throw createError('配置管理器未初始化', ERROR_CODES.CONFIG_SAVE_FAILED);
    }

    try {
      // 驗證更新的配置
      const newConfig = { ...this.config, ...updates };
      
      if (updates.hotkeys) {
        this.validateHotkeys(updates.hotkeys);
      }

      // 更新配置
      this.config = newConfig;
      
      // 儲存到持久化存儲
      this.store.store = this.config;
      
      this.emit('config-changed', this.config);
    } catch (error) {
      throw createError(
        `配置更新失敗: ${(error as Error).message}`,
        ERROR_CODES.CONFIG_SAVE_FAILED,
        error
      );
    }
  }

  /**
   * 更新快捷鍵配置
   */
  async updateHotkeys(hotkeys: Partial<HotkeyConfig>): Promise<void> {
    const newHotkeys = { ...this.config.hotkeys, ...hotkeys };
    this.validateHotkeys(newHotkeys);
    
    await this.updateConfig({ hotkeys: newHotkeys });
  }

  /**
   * 更新 Azure 配置
   */
  async updateAzureConfig(azure: Partial<AzureConfig>): Promise<void> {
    const newAzure = {
      openai: { ...this.config.azure.openai, ...azure.openai },
      speech: { ...this.config.azure.speech, ...azure.speech }
    };
    
    await this.updateConfig({ azure: newAzure });
  }

  /**
   * 更新音頻設備配置
   */
  async updateAudioDevice(device: AudioDeviceConfig): Promise<void> {
    await this.updateConfig({ audioDevice: device });
  }

  /**
   * 重置為預設配置
   */
  async resetToDefaults(): Promise<void> {
    try {
      this.config = { ...DEFAULT_CONFIG };
      this.store.clear();
      this.store.store = this.config;
      
      this.emit('config-reset', this.config);
    } catch (error) {
      throw createError(
        `重置配置失敗: ${(error as Error).message}`,
        ERROR_CODES.CONFIG_SAVE_FAILED,
        error
      );
    }
  }

  /**
   * 匯出配置
   */
  exportConfig(): string {
    const exportData = {
      ...this.config,
      // 移除敏感資訊
      azure: {
        openai: {
          ...this.config.azure.openai,
          apiKey: '***'
        },
        speech: {
          ...this.config.azure.speech,
          apiKey: '***'
        }
      }
    };
    
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 匯入配置
   */
  async importConfig(configJson: string): Promise<void> {
    try {
      const importedConfig = JSON.parse(configJson);
      
      // 驗證匯入的配置
      if (!this.isValidConfigStructure(importedConfig)) {
        throw new Error('配置格式無效');
      }
      
      // 合併配置（保留現有的 API 金鑰如果匯入的是 ***）
      const mergedConfig = this.mergeImportedConfig(importedConfig);
      
      await this.updateConfig(mergedConfig);
    } catch (error) {
      throw createError(
        `匯入配置失敗: ${(error as Error).message}`,
        ERROR_CODES.CONFIG_LOAD_FAILED,
        error
      );
    }
  }

  /**
   * 驗證快捷鍵配置
   */
  private validateHotkeys(hotkeys: Partial<HotkeyConfig>): void {
    if (hotkeys.aiSpeechToText && !isValidHotkey(hotkeys.aiSpeechToText)) {
      throw createError('AI 語音輸入快捷鍵格式無效', ERROR_CODES.CONFIG_SAVE_FAILED);
    }
    
    if (hotkeys.directSpeechToText && !isValidHotkey(hotkeys.directSpeechToText)) {
      throw createError('直接語音輸入快捷鍵格式無效', ERROR_CODES.CONFIG_SAVE_FAILED);
    }
    
    // 檢查快捷鍵是否重複
    if (hotkeys.aiSpeechToText && hotkeys.directSpeechToText && 
        hotkeys.aiSpeechToText === hotkeys.directSpeechToText) {
      throw createError('快捷鍵不能重複', ERROR_CODES.CONFIG_SAVE_FAILED);
    }
  }

  /**
   * 檢查配置結構是否有效
   */
  private isValidConfigStructure(config: any): boolean {
    return config && 
           typeof config === 'object' &&
           config.hotkeys &&
           config.azure &&
           config.audioDevice;
  }

  /**
   * 合併匯入的配置
   */
  private mergeImportedConfig(importedConfig: any): Partial<AppConfig> {
    const merged: Partial<AppConfig> = {};
    
    // 合併快捷鍵
    if (importedConfig.hotkeys) {
      merged.hotkeys = { ...this.config.hotkeys, ...importedConfig.hotkeys };
    }
    
    // 合併 Azure 配置（保留現有 API 金鑰如果匯入的是 ***）
    if (importedConfig.azure) {
      merged.azure = {
        openai: {
          ...this.config.azure.openai,
          ...importedConfig.azure.openai,
          apiKey: importedConfig.azure.openai.apiKey === '***' 
            ? this.config.azure.openai.apiKey 
            : importedConfig.azure.openai.apiKey
        },
        speech: {
          ...this.config.azure.speech,
          ...importedConfig.azure.speech,
          apiKey: importedConfig.azure.speech.apiKey === '***'
            ? this.config.azure.speech.apiKey
            : importedConfig.azure.speech.apiKey
        }
      };
    }
    
    // 合併其他配置
    if (importedConfig.audioDevice) {
      merged.audioDevice = importedConfig.audioDevice;
    }
    
    if (typeof importedConfig.autoStart === 'boolean') {
      merged.autoStart = importedConfig.autoStart;
    }
    
    if (typeof importedConfig.minimizeToTray === 'boolean') {
      merged.minimizeToTray = importedConfig.minimizeToTray;
    }
    
    return merged;
  }

  /**
   * 檢查配置是否完整
   */
  isConfigComplete(): boolean {
    return validateConfig(this.config);
  }

  /**
   * 獲取缺失的配置項目
   */
  getMissingConfigItems(): string[] {
    const missing: string[] = [];
    
    if (!this.config.azure.openai.endpoint) missing.push('Azure OpenAI Endpoint');
    if (!this.config.azure.openai.apiKey) missing.push('Azure OpenAI API Key');
    if (!this.config.azure.speech.region) missing.push('Azure Speech Region');
    if (!this.config.azure.speech.apiKey) missing.push('Azure Speech API Key');
    
    return missing;
  }

  /**
   * 銷毀配置管理器
   */
  destroy(): void {
    this.removeAllListeners();
  }
}
