"use strict";const e=require("electron"),o={getConfig:()=>e.ipcRenderer.invoke("get-config"),updateConfig:r=>e.ipcRenderer.invoke("update-config",r),startRecording:r=>e.ipcRenderer.invoke("start-recording",{mode:r}),stopRecording:()=>e.ipcRenderer.invoke("stop-recording"),getAudioDevices:()=>e.ipcRenderer.invoke("get-audio-devices"),minimizeToTray:()=>e.ipcRenderer.invoke("minimize-to-tray"),showWindow:()=>e.ipcRenderer.invoke("show-window"),onRecordingStateChanged:r=>(e.ipcRenderer.on("recording-state-changed",(i,n)=>r(n)),()=>e.ipcRenderer.removeAllListeners("recording-state-changed")),onConfigUpdated:r=>(e.ipcRenderer.on("config-updated",(i,n)=>r(n)),()=>e.ipcRenderer.removeAllListeners("config-updated")),onErrorOccurred:r=>(e.ipcRenderer.on("error-occurred",(i,n)=>r(n)),()=>e.ipcRenderer.removeAllListeners("error-occurred")),removeAllListeners:()=>{e.ipcRenderer.removeAllListeners("recording-state-changed"),e.ipcRenderer.removeAllListeners("config-updated"),e.ipcRenderer.removeAllListeners("error-occurred")}};e.contextBridge.exposeInMainWorld("electronAPI",o);
