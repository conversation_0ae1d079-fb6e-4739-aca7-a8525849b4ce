import { app, BrowserWindow, globalShortcut, ipcMain, Tray, Menu, nativeImage } from 'electron';
import { join } from 'path';
import { ConfigManager } from './services/ConfigManager';
import { AudioRecorder } from './services/AudioRecorder';
import { AzureSpeechService } from './services/AzureSpeechService';
import { AzureOpenAIService } from './services/AzureOpenAIService';
import { TextInputService } from './services/TextInputService';
import { ErrorHandler } from './services/ErrorHandler';
import { SpeechMode, RecordingState, AppConfig, RecordingSession } from '../shared/types';
import { WINDOW_CONFIG, APP_INFO } from '../shared/constants';
import { generateId } from '../shared/utils';

class SpeechPilotApp {
  private mainWindow: BrowserWindow | null = null;
  private recordingWindow: BrowserWindow | null = null;
  private tray: Tray | null = null;
  
  private configManager: ConfigManager;
  private audioRecorder: AudioRecorder;
  private speechService: AzureSpeechService;
  private openaiService: AzureOpenAIService;
  private textInputService: TextInputService;
  private errorHandler: ErrorHandler;
  
  private currentSession: RecordingSession | null = null;
  private isRecording = false;

  constructor() {
    this.configManager = new ConfigManager();
    this.audioRecorder = new AudioRecorder();
    this.speechService = new AzureSpeechService();
    this.openaiService = new AzureOpenAIService();
    this.textInputService = new TextInputService();
    this.errorHandler = new ErrorHandler();
    
    this.setupEventListeners();
  }

  async initialize() {
    await this.configManager.initialize();
    await this.createMainWindow();
    await this.createTray();
    await this.registerGlobalShortcuts();
    this.setupIPC();
  }

  private async createMainWindow() {
    this.mainWindow = new BrowserWindow({
      ...WINDOW_CONFIG.main,
      icon: this.getAppIcon(),
      webPreferences: {
        ...WINDOW_CONFIG.main.webPreferences,
        preload: join(__dirname, '../preload/preload.js')
      }
    });

    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3000');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(join(__dirname, '../renderer/index.html'));
    }

    this.mainWindow.on('close', (event) => {
      if (this.configManager.getConfig().minimizeToTray) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private async createRecordingWindow() {
    if (this.recordingWindow) return;

    this.recordingWindow = new BrowserWindow({
      ...WINDOW_CONFIG.recording,
      icon: this.getAppIcon(),
      webPreferences: {
        ...WINDOW_CONFIG.recording.webPreferences,
        preload: join(__dirname, '../preload/preload.js')
      }
    });

    if (process.env.NODE_ENV === 'development') {
      this.recordingWindow.loadURL('http://localhost:3000/recording');
    } else {
      this.recordingWindow.loadFile(join(__dirname, '../renderer/recording.html'));
    }

    this.recordingWindow.on('closed', () => {
      this.recordingWindow = null;
    });
  }

  private createTray() {
    const icon = this.getAppIcon();
    this.tray = new Tray(icon);
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '顯示主視窗',
        click: () => this.showMainWindow()
      },
      {
        label: '開始 AI 語音輸入',
        click: () => this.startRecording(SpeechMode.AI_SPEECH_TO_TEXT)
      },
      {
        label: '開始直接語音輸入',
        click: () => this.startRecording(SpeechMode.DIRECT_SPEECH_TO_TEXT)
      },
      { type: 'separator' },
      {
        label: '設定',
        click: () => this.showMainWindow()
      },
      {
        label: '退出',
        click: () => app.quit()
      }
    ]);
    
    this.tray.setContextMenu(contextMenu);
    this.tray.setToolTip(APP_INFO.name);
    
    this.tray.on('double-click', () => {
      this.showMainWindow();
    });
  }

  private getAppIcon() {
    const iconPath = join(__dirname, '../../assets/tray-icon.png');
    return nativeImage.createFromPath(iconPath);
  }

  private showMainWindow() {
    if (this.mainWindow) {
      this.mainWindow.show();
      this.mainWindow.focus();
    }
  }

  private async registerGlobalShortcuts() {
    const config = this.configManager.getConfig();
    
    // 註冊 AI 語音輸入快捷鍵
    globalShortcut.register(config.hotkeys.aiSpeechToText, () => {
      this.handleHotkeyPress(SpeechMode.AI_SPEECH_TO_TEXT);
    });
    
    // 註冊直接語音輸入快捷鍵
    globalShortcut.register(config.hotkeys.directSpeechToText, () => {
      this.handleHotkeyPress(SpeechMode.DIRECT_SPEECH_TO_TEXT);
    });
  }

  private handleHotkeyPress(mode: SpeechMode) {
    if (this.isRecording) {
      this.stopRecording();
    } else {
      this.startRecording(mode);
    }
  }

  private async startRecording(mode: SpeechMode) {
    if (this.isRecording) return;

    try {
      this.isRecording = true;
      this.currentSession = {
        id: generateId(),
        mode,
        state: RecordingState.RECORDING,
        startTime: Date.now()
      };

      await this.createRecordingWindow();
      this.recordingWindow?.show();
      
      await this.audioRecorder.startRecording();
      
      this.broadcastSessionUpdate();
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  private async stopRecording() {
    if (!this.isRecording || !this.currentSession) return;

    try {
      this.currentSession.state = RecordingState.PROCESSING;
      this.broadcastSessionUpdate();

      const audioBlob = await this.audioRecorder.stopRecording();
      this.currentSession.audioData = audioBlob;
      this.currentSession.endTime = Date.now();

      await this.processAudio();
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  private async processAudio() {
    if (!this.currentSession?.audioData) return;

    try {
      const config = this.configManager.getConfig();
      
      if (this.currentSession.mode === SpeechMode.DIRECT_SPEECH_TO_TEXT) {
        // 直接語音轉文字
        const result = await this.speechService.transcribe(
          this.currentSession.audioData,
          config.azure.speech
        );
        
        this.currentSession.transcription = result.text;
        await this.textInputService.typeText(result.text);
      } else {
        // AI 語音處理
        const aiResult = await this.openaiService.processAudio(
          this.currentSession.audioData,
          config.azure.openai
        );
        
        this.currentSession.transcription = aiResult.originalText;
        this.currentSession.aiResponse = aiResult.processedText;
        await this.textInputService.typeText(aiResult.processedText);
      }

      this.currentSession.state = RecordingState.COMPLETED;
      this.broadcastSessionUpdate();
      
      // 延遲關閉錄音視窗
      setTimeout(() => {
        this.recordingWindow?.close();
        this.isRecording = false;
        this.currentSession = null;
      }, 2000);
      
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  private setupEventListeners() {
    // 監聽配置變更
    this.configManager.on('config-changed', (config: AppConfig) => {
      this.mainWindow?.webContents.send('config-updated', config);
      this.updateGlobalShortcuts(config);
    });
  }

  private updateGlobalShortcuts(config: AppConfig) {
    globalShortcut.unregisterAll();
    this.registerGlobalShortcuts();
  }

  private setupIPC() {
    // 處理來自渲染程序的事件
    ipcMain.handle('get-config', () => {
      return this.configManager.getConfig();
    });

    ipcMain.handle('update-config', async (_, config: Partial<AppConfig>) => {
      await this.configManager.updateConfig(config);
    });

    ipcMain.handle('start-recording', (_, { mode }: { mode: SpeechMode }) => {
      this.startRecording(mode);
    });

    ipcMain.handle('stop-recording', () => {
      this.stopRecording();
    });

    ipcMain.handle('get-audio-devices', async () => {
      return await this.audioRecorder.getAudioDevices();
    });

    ipcMain.handle('minimize-to-tray', () => {
      this.mainWindow?.hide();
    });

    ipcMain.handle('show-window', () => {
      this.showMainWindow();
    });
  }

  private broadcastSessionUpdate() {
    if (this.currentSession) {
      this.mainWindow?.webContents.send('recording-state-changed', this.currentSession);
      this.recordingWindow?.webContents.send('recording-state-changed', this.currentSession);
    }
  }

  private handleError(error: Error) {
    this.currentSession.state = RecordingState.ERROR;
    this.currentSession.error = error.message;
    this.broadcastSessionUpdate();
    
    this.errorHandler.handleError(error);
    this.mainWindow?.webContents.send('error-occurred', {
      message: error.message,
      details: error
    });
    
    this.isRecording = false;
    setTimeout(() => {
      this.recordingWindow?.close();
      this.currentSession = null;
    }, 3000);
  }

  cleanup() {
    globalShortcut.unregisterAll();
    this.tray?.destroy();
  }
}

// 應用程式初始化
const speechPilotApp = new SpeechPilotApp();

app.whenReady().then(async () => {
  await speechPilotApp.initialize();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    speechPilotApp.initialize();
  }
});

app.on('before-quit', () => {
  speechPilotApp.cleanup();
});
